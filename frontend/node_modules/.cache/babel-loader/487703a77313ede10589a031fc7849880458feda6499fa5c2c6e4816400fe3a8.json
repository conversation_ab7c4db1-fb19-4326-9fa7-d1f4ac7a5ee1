{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/dashboard/DashboardPage.tsx\",\n  _s = $RefreshSig$();\n/**\n * Dashboard Page\n * Main dashboard with stats, charts, and overview\n */\nimport React, { useState, useEffect } from 'react';\nimport { Users, MessageSquare, Calendar, TrendingUp, Activity, Clock, CheckCircle, AlertCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  change,\n  changeType,\n  icon,\n  color\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-between\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm font-medium text-gray-600 mb-1\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm font-medium ${changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`,\n          children: change\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500 ml-1\",\n          children: \"vs last month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-3 rounded-lg ${color}`,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 27,\n  columnNumber: 3\n}, this);\n_c = StatCard;\nconst DashboardPage = () => {\n  _s();\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => setIsLoading(false), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const stats = [{\n    title: 'Total Users',\n    value: '2,847',\n    change: '+12.5%',\n    changeType: 'increase',\n    icon: /*#__PURE__*/_jsxDEV(Users, {\n      className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    color: 'bg-blue-50'\n  }, {\n    title: 'Messages Today',\n    value: '1,234',\n    change: '+8.2%',\n    changeType: 'increase',\n    icon: /*#__PURE__*/_jsxDEV(MessageSquare, {\n      className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    color: 'bg-green-50'\n  }, {\n    title: 'Bookings',\n    value: '156',\n    change: '+23.1%',\n    changeType: 'increase',\n    icon: /*#__PURE__*/_jsxDEV(Calendar, {\n      className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this),\n    color: 'bg-purple-50'\n  }, {\n    title: 'Response Time',\n    value: '1.2s',\n    change: '-15.3%',\n    changeType: 'decrease',\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this),\n    color: 'bg-orange-50'\n  }];\n  const recentActivities = [{\n    id: 1,\n    type: 'message',\n    user: 'John Doe',\n    action: 'sent a message',\n    time: '2 min ago',\n    status: 'success'\n  }, {\n    id: 2,\n    type: 'booking',\n    user: 'Jane Smith',\n    action: 'booked an appointment',\n    time: '5 min ago',\n    status: 'success'\n  }, {\n    id: 3,\n    type: 'user',\n    user: 'Mike Johnson',\n    action: 'registered',\n    time: '10 min ago',\n    status: 'success'\n  }, {\n    id: 4,\n    type: 'message',\n    user: 'Sarah Wilson',\n    action: 'sent a message',\n    time: '15 min ago',\n    status: 'warning'\n  }, {\n    id: 5,\n    type: 'booking',\n    user: 'Tom Brown',\n    action: 'cancelled booking',\n    time: '20 min ago',\n    status: 'error'\n  }];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 lg:p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-8 bg-gray-200 rounded w-1/4 mb-8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n          children: [...Array(4)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-200 h-32 rounded-xl\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-200 h-96 rounded-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-200 h-96 rounded-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 lg:px-8 py-8 space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-2\",\n            children: \"Welcome back! Here's what's happening today.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium\",\n            children: \"Export Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in-up\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            ...stat\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, stat.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Message Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                className: \"h-12 w-12 text-blue-400 mx-auto mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Chart visualization coming soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-6\",\n            children: \"Recent Activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: recentActivities.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors animate-fade-in\",\n              style: {\n                animationDelay: `${index * 100}ms`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-2 rounded-full ${activity.status === 'success' ? 'bg-green-100' : activity.status === 'warning' ? 'bg-yellow-100' : 'bg-red-100'}`,\n                children: activity.status === 'success' ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [activity.user, \" \", activity.action]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: activity.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, activity.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"Yt82d/dvZsn5nYh5sqDQjv+rJ38=\");\n_c2 = DashboardPage;\nexport default DashboardPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"DashboardPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Users", "MessageSquare", "Calendar", "TrendingUp", "Activity", "Clock", "CheckCircle", "AlertCircle", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "change", "changeType", "icon", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "DashboardPage", "_s", "isLoading", "setIsLoading", "timer", "setTimeout", "clearTimeout", "stats", "recentActivities", "id", "type", "user", "action", "time", "status", "Array", "map", "_", "i", "stat", "index", "style", "animationDelay", "activity", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/dashboard/DashboardPage.tsx"], "sourcesContent": ["/**\n * Dashboard Page\n * Main dashboard with stats, charts, and overview\n */\nimport React, { useState, useEffect } from 'react';\nimport { \n  Users, \n  MessageSquare, \n  Calendar, \n  TrendingUp, \n  Activity,\n  Clock,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\n\ninterface StatCardProps {\n  title: string;\n  value: string;\n  change: string;\n  changeType: 'increase' | 'decrease';\n  icon: React.ReactNode;\n  color: string;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({ title, value, change, changeType, icon, color }) => (\n  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1\">\n    <div className=\"flex items-center justify-between\">\n      <div>\n        <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n        <p className=\"text-3xl font-bold text-gray-900\">{value}</p>\n        <div className=\"flex items-center mt-2\">\n          <span className={`text-sm font-medium ${\n            changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n          }`}>\n            {change}\n          </span>\n          <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n        </div>\n      </div>\n      <div className={`p-3 rounded-lg ${color}`}>\n        {icon}\n      </div>\n    </div>\n  </div>\n);\n\nconst DashboardPage: React.FC = () => {\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => setIsLoading(false), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const stats = [\n    {\n      title: 'Total Users',\n      value: '2,847',\n      change: '+12.5%',\n      changeType: 'increase' as const,\n      icon: <Users className=\"h-6 w-6 text-blue-600\" />,\n      color: 'bg-blue-50'\n    },\n    {\n      title: 'Messages Today',\n      value: '1,234',\n      change: '+8.2%',\n      changeType: 'increase' as const,\n      icon: <MessageSquare className=\"h-6 w-6 text-green-600\" />,\n      color: 'bg-green-50'\n    },\n    {\n      title: 'Bookings',\n      value: '156',\n      change: '+23.1%',\n      changeType: 'increase' as const,\n      icon: <Calendar className=\"h-6 w-6 text-purple-600\" />,\n      color: 'bg-purple-50'\n    },\n    {\n      title: 'Response Time',\n      value: '1.2s',\n      change: '-15.3%',\n      changeType: 'decrease' as const,\n      icon: <Clock className=\"h-6 w-6 text-orange-600\" />,\n      color: 'bg-orange-50'\n    }\n  ];\n\n  const recentActivities = [\n    { id: 1, type: 'message', user: 'John Doe', action: 'sent a message', time: '2 min ago', status: 'success' },\n    { id: 2, type: 'booking', user: 'Jane Smith', action: 'booked an appointment', time: '5 min ago', status: 'success' },\n    { id: 3, type: 'user', user: 'Mike Johnson', action: 'registered', time: '10 min ago', status: 'success' },\n    { id: 4, type: 'message', user: 'Sarah Wilson', action: 'sent a message', time: '15 min ago', status: 'warning' },\n    { id: 5, type: 'booking', user: 'Tom Brown', action: 'cancelled booking', time: '20 min ago', status: 'error' }\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"p-6 lg:p-8\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-8\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"bg-gray-200 h-32 rounded-xl\"></div>\n            ))}\n          </div>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <div className=\"bg-gray-200 h-96 rounded-xl\"></div>\n            <div className=\"bg-gray-200 h-96 rounded-xl\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8 py-8 space-y-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n            <p className=\"text-gray-600 mt-2\">Welcome back! Here's what's happening today.</p>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <button className=\"bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium\">\n              Export Data\n            </button>\n          </div>\n        </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => (\n          <div\n            key={stat.title}\n            className=\"animate-fade-in-up\"\n            style={{ animationDelay: `${index * 100}ms` }}\n          >\n            <StatCard {...stat} />\n          </div>\n        ))}\n      </div>\n\n      {/* Charts and Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Chart Placeholder */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Message Analytics</h3>\n            <TrendingUp className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          <div className=\"h-64 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center\">\n            <div className=\"text-center\">\n              <Activity className=\"h-12 w-12 text-blue-400 mx-auto mb-3\" />\n              <p className=\"text-gray-600\">Chart visualization coming soon</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Recent Activity</h3>\n          <div className=\"space-y-4\">\n            {recentActivities.map((activity, index) => (\n              <div\n                key={activity.id}\n                className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors animate-fade-in\"\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <div className={`p-2 rounded-full ${\n                  activity.status === 'success' ? 'bg-green-100' :\n                  activity.status === 'warning' ? 'bg-yellow-100' : 'bg-red-100'\n                }`}>\n                  {activity.status === 'success' ? (\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n                  )}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900\">\n                    {activity.user} {activity.action}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtB,MAAMC,QAAiC,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC,UAAU;EAAEC,IAAI;EAAEC;AAAM,CAAC,kBAC1FP,OAAA;EAAKQ,SAAS,EAAC,qIAAqI;EAAAC,QAAA,eAClJT,OAAA;IAAKQ,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDT,OAAA;MAAAS,QAAA,gBACET,OAAA;QAAGQ,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEb,OAAA;QAAGQ,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAEN;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3Db,OAAA;QAAKQ,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCT,OAAA;UAAMQ,SAAS,EAAE,uBACfH,UAAU,KAAK,UAAU,GAAG,gBAAgB,GAAG,cAAc,EAC5D;UAAAI,QAAA,EACAL;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPb,OAAA;UAAMQ,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNb,OAAA;MAAKQ,SAAS,EAAE,kBAAkBD,KAAK,EAAG;MAAAE,QAAA,EACvCH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GApBIb,QAAiC;AAsBvC,MAAMc,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd;IACA,MAAM6B,KAAK,GAAGC,UAAU,CAAC,MAAMF,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACzD,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,KAAK,GAAG,CACZ;IACEpB,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,UAAmB;IAC/BC,IAAI,eAAEN,OAAA,CAACT,KAAK;MAACiB,SAAS,EAAC;IAAuB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjDN,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,UAAmB;IAC/BC,IAAI,eAAEN,OAAA,CAACR,aAAa;MAACgB,SAAS,EAAC;IAAwB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1DN,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,UAAmB;IAC/BC,IAAI,eAAEN,OAAA,CAACP,QAAQ;MAACe,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtDN,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,UAAmB;IAC/BC,IAAI,eAAEN,OAAA,CAACJ,KAAK;MAACY,SAAS,EAAC;IAAyB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnDN,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMgB,gBAAgB,GAAG,CACvB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,MAAM,EAAE,gBAAgB;IAAEC,IAAI,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC5G;IAAEL,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE,uBAAuB;IAAEC,IAAI,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAU,CAAC,EACrH;IAAEL,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,cAAc;IAAEC,MAAM,EAAE,YAAY;IAAEC,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC1G;IAAEL,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,cAAc;IAAEC,MAAM,EAAE,gBAAgB;IAAEC,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE;EAAU,CAAC,EACjH;IAAEL,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,WAAW;IAAEC,MAAM,EAAE,mBAAmB;IAAEC,IAAI,EAAE,YAAY;IAAEC,MAAM,EAAE;EAAQ,CAAC,CAChH;EAED,IAAIZ,SAAS,EAAE;IACb,oBACEjB,OAAA;MAAKQ,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBT,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BT,OAAA;UAAKQ,SAAS,EAAC;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Db,OAAA;UAAKQ,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EACvE,CAAC,GAAGqB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBjC,OAAA;YAAaQ,SAAS,EAAC;UAA6B,GAA1CyB,CAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA+C,CAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDT,OAAA;YAAKQ,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDb,OAAA;YAAKQ,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEb,OAAA;IAAKQ,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCT,OAAA;MAAKQ,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAE5DT,OAAA;QAAKQ,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDT,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAIQ,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/Db,OAAA;YAAGQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CT,OAAA;YAAQQ,SAAS,EAAC,6FAA6F;YAAAC,QAAA,EAAC;UAEhH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGRb,OAAA;QAAKQ,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEa,KAAK,CAACS,GAAG,CAAC,CAACG,IAAI,EAAEC,KAAK,kBACrBnC,OAAA;UAEEQ,SAAS,EAAC,oBAAoB;UAC9B4B,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAA1B,QAAA,eAE9CT,OAAA,CAACC,QAAQ;YAAA,GAAKiC;UAAI;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC,GAJjBqB,IAAI,CAAChC,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNb,OAAA;QAAKQ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDT,OAAA;UAAKQ,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvET,OAAA;YAAKQ,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDT,OAAA;cAAIQ,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Eb,OAAA,CAACN,UAAU;cAACc,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC,8FAA8F;YAAAC,QAAA,eAC3GT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BT,OAAA,CAACL,QAAQ;gBAACa,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7Db,OAAA;gBAAGQ,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNb,OAAA;UAAKQ,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvET,OAAA;YAAIQ,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7Eb,OAAA;YAAKQ,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBc,gBAAgB,CAACQ,GAAG,CAAC,CAACO,QAAQ,EAAEH,KAAK,kBACpCnC,OAAA;cAEEQ,SAAS,EAAC,+FAA+F;cACzG4B,KAAK,EAAE;gBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;cAAK,CAAE;cAAA1B,QAAA,gBAE9CT,OAAA;gBAAKQ,SAAS,EAAE,oBACd8B,QAAQ,CAACT,MAAM,KAAK,SAAS,GAAG,cAAc,GAC9CS,QAAQ,CAACT,MAAM,KAAK,SAAS,GAAG,eAAe,GAAG,YAAY,EAC7D;gBAAApB,QAAA,EACA6B,QAAQ,CAACT,MAAM,KAAK,SAAS,gBAC5B7B,OAAA,CAACH,WAAW;kBAACW,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAElDb,OAAA,CAACF,WAAW;kBAACU,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACnD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNb,OAAA;gBAAKQ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BT,OAAA;kBAAGQ,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAC7C6B,QAAQ,CAACZ,IAAI,EAAC,GAAC,EAACY,QAAQ,CAACX,MAAM;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACJb,OAAA;kBAAGQ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE6B,QAAQ,CAACV;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA,GAnBDyB,QAAQ,CAACd,EAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACG,EAAA,CAtJID,aAAuB;AAAAwB,GAAA,GAAvBxB,aAAuB;AAwJ7B,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}