{"ast": null, "code": "/**\n * Base HTTP service using Axios\n * Handles authentication, interceptors, and common configurations\n */import axios,{InternalAxiosRequestConfig}from'axios';// Extend the Axios config interface to include metadata\nclass BaseHttpService{constructor(){this.api=void 0;this.baseURL=void 0;this.baseURL=process.env.REACT_APP_API_URL||'http://localhost:8000';this.api=axios.create({baseURL:this.baseURL,timeout:30000,withCredentials:true,// Include cookies in requests\nheaders:{'Content-Type':'application/json'}});this.setupInterceptors();}setupInterceptors(){// Request interceptor\nthis.api.interceptors.request.use(config=>{var _config$method;// Add request timestamp for debugging\nconfig.metadata={startTime:new Date()};console.log(\"\\uD83D\\uDE80 \".concat((_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),\" \").concat(config.url),{data:config.data,params:config.params});return config;},error=>{console.error('❌ Request Error:',error);return Promise.reject(error);});// Response interceptor\nthis.api.interceptors.response.use(response=>{var _response$config$meta,_response$config$meta2,_response$config$meth;const duration=new Date().getTime()-(((_response$config$meta=response.config.metadata)===null||_response$config$meta===void 0?void 0:(_response$config$meta2=_response$config$meta.startTime)===null||_response$config$meta2===void 0?void 0:_response$config$meta2.getTime())||0);console.log(\"\\u2705 \".concat(response.status,\" \").concat((_response$config$meth=response.config.method)===null||_response$config$meth===void 0?void 0:_response$config$meth.toUpperCase(),\" \").concat(response.config.url,\" (\").concat(duration,\"ms)\"),{data:response.data});return response;},error=>{var _error$config,_error$config$metadat,_error$response,_error$config2,_error$config2$method,_error$config3,_error$response2,_error$response3;const duration=(_error$config=error.config)!==null&&_error$config!==void 0&&(_error$config$metadat=_error$config.metadata)!==null&&_error$config$metadat!==void 0&&_error$config$metadat.startTime?new Date().getTime()-error.config.metadata.startTime.getTime():0;console.error(\"\\u274C \".concat(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)||'Network',\" \").concat((_error$config2=error.config)===null||_error$config2===void 0?void 0:(_error$config2$method=_error$config2.method)===null||_error$config2$method===void 0?void 0:_error$config2$method.toUpperCase(),\" \").concat((_error$config3=error.config)===null||_error$config3===void 0?void 0:_error$config3.url,\" (\").concat(duration,\"ms)\"),{error:((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data)||error.message});// Handle specific error cases\nif(((_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.status)===401){this.handleUnauthorized();}return Promise.reject(this.formatError(error));});}handleUnauthorized(){// Clear auth data\nlocalStorage.removeItem('user_data');// Redirect to login if not already there\nif(window.location.pathname!=='/auth/login'){window.location.href='/auth/login';}}formatError(error){if(error.response){var _error$response$data,_error$response$data2;// Server responded with error status\nreturn{status:error.response.status,message:((_error$response$data=error.response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||((_error$response$data2=error.response.data)===null||_error$response$data2===void 0?void 0:_error$response$data2.message)||'Server error',data:error.response.data};}else if(error.request){// Request was made but no response received\nreturn{status:0,message:'Network error - please check your connection',data:null};}else{// Something else happened\nreturn{status:0,message:error.message||'Unknown error occurred',data:null};}}// HTTP Methods\nasync get(url,config){const response=await this.api.get(url,config);return response.data;}async post(url,data,config){const response=await this.api.post(url,data,config);return response.data;}async put(url,data,config){const response=await this.api.put(url,data,config);return response.data;}async patch(url,data,config){const response=await this.api.patch(url,data,config);return response.data;}async delete(url,config){const response=await this.api.delete(url,config);return response.data;}// Utility methods (kept for compatibility but not used in session-based auth)\nsetAuthToken(token){// No-op for session-based auth\n}clearAuthToken(){localStorage.removeItem('user_data');}getBaseURL(){return this.baseURL;}// File upload helper\nasync uploadFile(url,file,onProgress){const formData=new FormData();formData.append('file',file);const config={headers:{'Content-Type':'multipart/form-data'},onUploadProgress:progressEvent=>{if(onProgress&&progressEvent.total){const progress=Math.round(progressEvent.loaded*100/progressEvent.total);onProgress(progress);}}};const response=await this.api.post(url,formData,config);return response.data;}// Health check\nasync healthCheck(){return this.get('/health');}}// Create and export singleton instance\nconst baseHttp=new BaseHttpService();export default baseHttp;// Export the class for testing purposes\nexport{BaseHttpService};", "map": {"version": 3, "names": ["axios", "InternalAxiosRequestConfig", "BaseHttpService", "constructor", "api", "baseURL", "process", "env", "REACT_APP_API_URL", "create", "timeout", "withCredentials", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "_config$method", "metadata", "startTime", "Date", "console", "log", "concat", "method", "toUpperCase", "url", "data", "params", "error", "Promise", "reject", "response", "_response$config$meta", "_response$config$meta2", "_response$config$meth", "duration", "getTime", "status", "_error$config", "_error$config$metadat", "_error$response", "_error$config2", "_error$config2$method", "_error$config3", "_error$response2", "_error$response3", "message", "handleUnauthorized", "formatError", "localStorage", "removeItem", "window", "location", "pathname", "href", "_error$response$data", "_error$response$data2", "detail", "get", "post", "put", "patch", "delete", "setAuthToken", "token", "clearAuthToken", "getBaseURL", "uploadFile", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "healthCheck", "baseHttp"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/baseHttp.ts"], "sourcesContent": ["/**\n * Base HTTP service using Axios\n * Handles authentication, interceptors, and common configurations\n */\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\n\n// Extend the Axios config interface to include metadata\ndeclare module 'axios' {\n  interface InternalAxiosRequestConfig {\n    metadata?: {\n      startTime: Date;\n    };\n  }\n}\n\nclass BaseHttpService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n    \n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      withCredentials: true, // Include cookies in requests\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      (config: InternalAxiosRequestConfig) => {\n        // Add request timestamp for debugging\n        config.metadata = { startTime: new Date() };\n\n        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {\n          data: config.data,\n          params: config.params,\n        });\n\n        return config;\n      },\n      (error) => {\n        console.error('❌ Request Error:', error);\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        const duration = new Date().getTime() - (response.config.metadata?.startTime?.getTime() || 0);\n        \n        console.log(`✅ ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {\n          data: response.data,\n        });\n\n        return response;\n      },\n      (error) => {\n        const duration = error.config?.metadata?.startTime\n          ? new Date().getTime() - error.config.metadata.startTime.getTime()\n          : 0;\n\n        console.error(`❌ ${error.response?.status || 'Network'} ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {\n          error: error.response?.data || error.message,\n        });\n\n        // Handle specific error cases\n        if (error.response?.status === 401) {\n          this.handleUnauthorized();\n        }\n\n        return Promise.reject(this.formatError(error));\n      }\n    );\n  }\n\n  private handleUnauthorized(): void {\n    // Clear auth data\n    localStorage.removeItem('user_data');\n\n    // Redirect to login if not already there\n    if (window.location.pathname !== '/auth/login') {\n      window.location.href = '/auth/login';\n    }\n  }\n\n  private formatError(error: any): any {\n    if (error.response) {\n      // Server responded with error status\n      return {\n        status: error.response.status,\n        message: error.response.data?.detail || error.response.data?.message || 'Server error',\n        data: error.response.data,\n      };\n    } else if (error.request) {\n      // Request was made but no response received\n      return {\n        status: 0,\n        message: 'Network error - please check your connection',\n        data: null,\n      };\n    } else {\n      // Something else happened\n      return {\n        status: 0,\n        message: error.message || 'Unknown error occurred',\n        data: null,\n      };\n    }\n  }\n\n  // HTTP Methods\n  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.get<T>(url, config);\n    return response.data;\n  }\n\n  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.post<T>(url, data, config);\n    return response.data;\n  }\n\n  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.put<T>(url, data, config);\n    return response.data;\n  }\n\n  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.patch<T>(url, data, config);\n    return response.data;\n  }\n\n  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.delete<T>(url, config);\n    return response.data;\n  }\n\n  // Utility methods (kept for compatibility but not used in session-based auth)\n  setAuthToken(token: string): void {\n    // No-op for session-based auth\n  }\n\n  clearAuthToken(): void {\n    localStorage.removeItem('user_data');\n  }\n\n  getBaseURL(): string {\n    return this.baseURL;\n  }\n\n  // File upload helper\n  async uploadFile<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    const config: AxiosRequestConfig = {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    };\n\n    const response = await this.api.post<T>(url, formData, config);\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck(): Promise<any> {\n    return this.get('/health');\n  }\n}\n\n// Create and export singleton instance\nconst baseHttp = new BaseHttpService();\nexport default baseHttp;\n\n// Export the class for testing purposes\nexport { BaseHttpService };\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAsDC,0BAA0B,KAAQ,OAAO,CAE3G;AASA,KAAM,CAAAC,eAAgB,CAIpBC,WAAWA,CAAA,CAAG,MAHNC,GAAG,aACHC,OAAO,QAGb,IAAI,CAACA,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAEvE,IAAI,CAACJ,GAAG,CAAGJ,KAAK,CAACS,MAAM,CAAC,CACtBJ,OAAO,CAAE,IAAI,CAACA,OAAO,CACrBK,OAAO,CAAE,KAAK,CACdC,eAAe,CAAE,IAAI,CAAE;AACvBC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAC1B,CAEQA,iBAAiBA,CAAA,CAAS,CAChC;AACA,IAAI,CAACT,GAAG,CAACU,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAkC,EAAK,KAAAC,cAAA,CACtC;AACAD,MAAM,CAACE,QAAQ,CAAG,CAAEC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAE,CAAC,CAE3CC,OAAO,CAACC,GAAG,iBAAAC,MAAA,EAAAN,cAAA,CAAOD,MAAM,CAACQ,MAAM,UAAAP,cAAA,iBAAbA,cAAA,CAAeQ,WAAW,CAAC,CAAC,MAAAF,MAAA,CAAIP,MAAM,CAACU,GAAG,EAAI,CAC9DC,IAAI,CAAEX,MAAM,CAACW,IAAI,CACjBC,MAAM,CAAEZ,MAAM,CAACY,MACjB,CAAC,CAAC,CAEF,MAAO,CAAAZ,MAAM,CACf,CAAC,CACAa,KAAK,EAAK,CACTR,OAAO,CAACQ,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,IAAI,CAAC1B,GAAG,CAACU,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CAC/BiB,QAAuB,EAAK,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAC3B,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAhB,IAAI,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,EAAI,EAAAJ,qBAAA,CAAAD,QAAQ,CAAChB,MAAM,CAACE,QAAQ,UAAAe,qBAAA,kBAAAC,sBAAA,CAAxBD,qBAAA,CAA0Bd,SAAS,UAAAe,sBAAA,iBAAnCA,sBAAA,CAAqCG,OAAO,CAAC,CAAC,GAAI,CAAC,CAAC,CAE7FhB,OAAO,CAACC,GAAG,WAAAC,MAAA,CAAMS,QAAQ,CAACM,MAAM,MAAAf,MAAA,EAAAY,qBAAA,CAAIH,QAAQ,CAAChB,MAAM,CAACQ,MAAM,UAAAW,qBAAA,iBAAtBA,qBAAA,CAAwBV,WAAW,CAAC,CAAC,MAAAF,MAAA,CAAIS,QAAQ,CAAChB,MAAM,CAACU,GAAG,OAAAH,MAAA,CAAKa,QAAQ,QAAO,CAClHT,IAAI,CAAEK,QAAQ,CAACL,IACjB,CAAC,CAAC,CAEF,MAAO,CAAAK,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,KAAAU,aAAA,CAAAC,qBAAA,CAAAC,eAAA,CAAAC,cAAA,CAAAC,qBAAA,CAAAC,cAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACT,KAAM,CAAAV,QAAQ,CAAG,CAAAG,aAAA,CAAAV,KAAK,CAACb,MAAM,UAAAuB,aAAA,YAAAC,qBAAA,CAAZD,aAAA,CAAcrB,QAAQ,UAAAsB,qBAAA,WAAtBA,qBAAA,CAAwBrB,SAAS,CAC9C,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,CAAGR,KAAK,CAACb,MAAM,CAACE,QAAQ,CAACC,SAAS,CAACkB,OAAO,CAAC,CAAC,CAChE,CAAC,CAELhB,OAAO,CAACQ,KAAK,WAAAN,MAAA,CAAM,EAAAkB,eAAA,CAAAZ,KAAK,CAACG,QAAQ,UAAAS,eAAA,iBAAdA,eAAA,CAAgBH,MAAM,GAAI,SAAS,MAAAf,MAAA,EAAAmB,cAAA,CAAIb,KAAK,CAACb,MAAM,UAAA0B,cAAA,kBAAAC,qBAAA,CAAZD,cAAA,CAAclB,MAAM,UAAAmB,qBAAA,iBAApBA,qBAAA,CAAsBlB,WAAW,CAAC,CAAC,MAAAF,MAAA,EAAAqB,cAAA,CAAIf,KAAK,CAACb,MAAM,UAAA4B,cAAA,iBAAZA,cAAA,CAAclB,GAAG,OAAAH,MAAA,CAAKa,QAAQ,QAAO,CACpIP,KAAK,CAAE,EAAAgB,gBAAA,CAAAhB,KAAK,CAACG,QAAQ,UAAAa,gBAAA,iBAAdA,gBAAA,CAAgBlB,IAAI,GAAIE,KAAK,CAACkB,OACvC,CAAC,CAAC,CAEF;AACA,GAAI,EAAAD,gBAAA,CAAAjB,KAAK,CAACG,QAAQ,UAAAc,gBAAA,iBAAdA,gBAAA,CAAgBR,MAAM,IAAK,GAAG,CAAE,CAClC,IAAI,CAACU,kBAAkB,CAAC,CAAC,CAC3B,CAEA,MAAO,CAAAlB,OAAO,CAACC,MAAM,CAAC,IAAI,CAACkB,WAAW,CAACpB,KAAK,CAAC,CAAC,CAChD,CACF,CAAC,CACH,CAEQmB,kBAAkBA,CAAA,CAAS,CACjC;AACAE,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAEpC;AACA,GAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC9CF,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAG,aAAa,CACtC,CACF,CAEQN,WAAWA,CAACpB,KAAU,CAAO,CACnC,GAAIA,KAAK,CAACG,QAAQ,CAAE,KAAAwB,oBAAA,CAAAC,qBAAA,CAClB;AACA,MAAO,CACLnB,MAAM,CAAET,KAAK,CAACG,QAAQ,CAACM,MAAM,CAC7BS,OAAO,CAAE,EAAAS,oBAAA,CAAA3B,KAAK,CAACG,QAAQ,CAACL,IAAI,UAAA6B,oBAAA,iBAAnBA,oBAAA,CAAqBE,MAAM,KAAAD,qBAAA,CAAI5B,KAAK,CAACG,QAAQ,CAACL,IAAI,UAAA8B,qBAAA,iBAAnBA,qBAAA,CAAqBV,OAAO,GAAI,cAAc,CACtFpB,IAAI,CAAEE,KAAK,CAACG,QAAQ,CAACL,IACvB,CAAC,CACH,CAAC,IAAM,IAAIE,KAAK,CAACf,OAAO,CAAE,CACxB;AACA,MAAO,CACLwB,MAAM,CAAE,CAAC,CACTS,OAAO,CAAE,8CAA8C,CACvDpB,IAAI,CAAE,IACR,CAAC,CACH,CAAC,IAAM,CACL;AACA,MAAO,CACLW,MAAM,CAAE,CAAC,CACTS,OAAO,CAAElB,KAAK,CAACkB,OAAO,EAAI,wBAAwB,CAClDpB,IAAI,CAAE,IACR,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAgC,GAAGA,CAAUjC,GAAW,CAAEV,MAA2B,CAAc,CACvE,KAAM,CAAAgB,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAACwD,GAAG,CAAIjC,GAAG,CAAEV,MAAM,CAAC,CACnD,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA,KAAM,CAAAiC,IAAIA,CAAUlC,GAAW,CAAEC,IAAU,CAAEX,MAA2B,CAAc,CACpF,KAAM,CAAAgB,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAACyD,IAAI,CAAIlC,GAAG,CAAEC,IAAI,CAAEX,MAAM,CAAC,CAC1D,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA,KAAM,CAAAkC,GAAGA,CAAUnC,GAAW,CAAEC,IAAU,CAAEX,MAA2B,CAAc,CACnF,KAAM,CAAAgB,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAAC0D,GAAG,CAAInC,GAAG,CAAEC,IAAI,CAAEX,MAAM,CAAC,CACzD,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA,KAAM,CAAAmC,KAAKA,CAAUpC,GAAW,CAAEC,IAAU,CAAEX,MAA2B,CAAc,CACrF,KAAM,CAAAgB,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAAC2D,KAAK,CAAIpC,GAAG,CAAEC,IAAI,CAAEX,MAAM,CAAC,CAC3D,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA,KAAM,CAAAoC,MAAMA,CAAUrC,GAAW,CAAEV,MAA2B,CAAc,CAC1E,KAAM,CAAAgB,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAAC4D,MAAM,CAAIrC,GAAG,CAAEV,MAAM,CAAC,CACtD,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA;AACAqC,YAAYA,CAACC,KAAa,CAAQ,CAChC;AAAA,CAGFC,cAAcA,CAAA,CAAS,CACrBhB,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CACtC,CAEAgB,UAAUA,CAAA,CAAW,CACnB,MAAO,KAAI,CAAC/D,OAAO,CACrB,CAEA;AACA,KAAM,CAAAgE,UAAUA,CAAU1C,GAAW,CAAE2C,IAAU,CAAEC,UAAuC,CAAc,CACtG,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEJ,IAAI,CAAC,CAE7B,KAAM,CAAArD,MAA0B,CAAG,CACjCL,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CAAC,CACD+D,gBAAgB,CAAGC,aAAa,EAAK,CACnC,GAAIL,UAAU,EAAIK,aAAa,CAACC,KAAK,CAAE,CACrC,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAM,CAAG,GAAG,CAAIL,aAAa,CAACC,KAAK,CAAC,CAC/EN,UAAU,CAACO,QAAQ,CAAC,CACtB,CACF,CACF,CAAC,CAED,KAAM,CAAA7C,QAAQ,CAAG,KAAM,KAAI,CAAC7B,GAAG,CAACyD,IAAI,CAAIlC,GAAG,CAAE6C,QAAQ,CAAEvD,MAAM,CAAC,CAC9D,MAAO,CAAAgB,QAAQ,CAACL,IAAI,CACtB,CAEA;AACA,KAAM,CAAAsD,WAAWA,CAAA,CAAiB,CAChC,MAAO,KAAI,CAACtB,GAAG,CAAC,SAAS,CAAC,CAC5B,CACF,CAEA;AACA,KAAM,CAAAuB,QAAQ,CAAG,GAAI,CAAAjF,eAAe,CAAC,CAAC,CACtC,cAAe,CAAAiF,QAAQ,CAEvB;AACA,OAASjF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}