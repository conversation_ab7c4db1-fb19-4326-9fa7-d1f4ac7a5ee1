{"ast": null, "code": "/**\n * Protected Layout Component\n * Used for authenticated pages - includes sidebar navigation\n */import React,{useState}from'react';import{useNavigate,useLocation}from'react-router-dom';import{LayoutDashboard,PlayCircle,Megaphone,Settings,LogOut,Menu,X,User,Bell}from'lucide-react';import{authService}from'../services';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedLayout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const navigate=useNavigate();const location=useLocation();const navigation=[{name:'Dashboard',href:'/dashboard',icon:LayoutDashboard},{name:'Playground',href:'/playground',icon:PlayCircle},{name:'CTA Page',href:'/cta',icon:Megaphone},{name:'Setting<PERSON>',href:'/settings',icon:Settings}];const handleLogout=async()=>{await authService.logout();};const isActive=path=>location.pathname===path;return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[sidebarOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40 lg:hidden\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"\\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\\n        \".concat(sidebarOpen?'translate-x-0':'-translate-x-full',\"\\n      \"),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 px-6 bg-gradient-to-r from-blue-600 to-indigo-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"CS\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-lg\",children:\"Chat System\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSidebarOpen(false),className:\"lg:hidden text-white hover:bg-white/20 p-1 rounded\",children:/*#__PURE__*/_jsx(X,{className:\"h-5 w-5\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 px-4 py-6 space-y-2\",children:navigation.map(item=>{const Icon=item.icon;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{navigate(item.href);setSidebarOpen(false);},className:\"\\n                    w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200\\n                    \".concat(isActive(item.href)?'bg-blue-50 text-blue-700 border-r-2 border-blue-700':'text-gray-600 hover:bg-gray-50 hover:text-gray-900',\"\\n                  \"),children:[/*#__PURE__*/_jsx(Icon,{className:\"mr-3 h-5 w-5 \".concat(isActive(item.href)?'text-blue-700':'text-gray-400')}),item.name]},item.name);})}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-gray-200 p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(User,{className:\"h-5 w-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:\"Demo User\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 truncate\",children:\"<EMAIL>\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleLogout,className:\"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors\",children:[/*#__PURE__*/_jsx(LogOut,{className:\"mr-3 h-4 w-4\"}),\"Sign out\"]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:pl-64\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"sticky top-0 z-30 bg-white shadow-sm border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSidebarOpen(true),className:\"lg:hidden text-gray-500 hover:text-gray-900\",children:/*#__PURE__*/_jsx(Menu,{className:\"h-6 w-6\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4 ml-auto\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"text-gray-400 hover:text-gray-500 relative\",children:[/*#__PURE__*/_jsx(Bell,{className:\"h-6 w-6\"}),/*#__PURE__*/_jsx(\"span\",{className:\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"})]})})]})}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1\",children:children})]})]});};export default ProtectedLayout;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "LayoutDashboard", "PlayCircle", "Megaphone", "Settings", "LogOut", "<PERSON><PERSON>", "X", "User", "Bell", "authService", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedLayout", "_ref", "children", "sidebarOpen", "setSidebarOpen", "navigate", "location", "navigation", "name", "href", "icon", "handleLogout", "logout", "isActive", "path", "pathname", "className", "onClick", "concat", "map", "item", "Icon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/ProtectedLayout.tsx"], "sourcesContent": ["/**\n * Protected Layout Component\n * Used for authenticated pages - includes sidebar navigation\n */\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { \n  LayoutDashboard, \n  PlayCircle, \n  Megaphone, \n  Settings, \n  LogOut, \n  Menu, \n  X,\n  User,\n  Bell\n} from 'lucide-react';\nimport { authService } from '../services';\n\ninterface ProtectedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n    { name: 'Playground', href: '/playground', icon: PlayCircle },\n    { name: 'CTA Page', href: '/cta', icon: Megaphone },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ];\n\n  const handleLogout = async () => {\n    await authService.logout();\n  };\n\n  const isActive = (path: string) => location.pathname === path;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        >\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity\"></div>\n        </div>\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center justify-between h-16 px-6 bg-gradient-to-r from-blue-600 to-indigo-600\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">CS</span>\n              </div>\n              <span className=\"text-white font-bold text-lg\">Chat System</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden text-white hover:bg-white/20 p-1 rounded\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <button\n                  key={item.name}\n                  onClick={() => {\n                    navigate(item.href);\n                    setSidebarOpen(false);\n                  }}\n                  className={`\n                    w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200\n                    ${isActive(item.href)\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <Icon className={`mr-3 h-5 w-5 ${isActive(item.href) ? 'text-blue-700' : 'text-gray-400'}`} />\n                  {item.name}\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* User section */}\n          <div className=\"border-t border-gray-200 p-4\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                <User className=\"h-5 w-5 text-white\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">Demo User</p>\n                <p className=\"text-xs text-gray-500 truncate\"><EMAIL></p>\n              </div>\n            </div>\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n            >\n              <LogOut className=\"mr-3 h-4 w-4\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-30 bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-900\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4 ml-auto\">\n              <button className=\"text-gray-400 hover:text-gray-500 relative\">\n                <Bell className=\"h-6 w-6\" />\n                <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default ProtectedLayout;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,eAAe,CACfC,UAAU,CACVC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,IAAI,CACJC,CAAC,CACDC,IAAI,CACJC,IAAI,KACC,cAAc,CACrB,OAASC,WAAW,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM1C,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACnE,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAsB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAsB,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAExB,eAAgB,CAAC,CAChE,CAAEsB,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAEvB,UAAW,CAAC,CAC7D,CAAEqB,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAEtB,SAAU,CAAC,CACnD,CAAEoB,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAErB,QAAS,CAAC,CACxD,CAED,KAAM,CAAAsB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAhB,WAAW,CAACiB,MAAM,CAAC,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAIC,IAAY,EAAKR,QAAQ,CAACS,QAAQ,GAAKD,IAAI,CAE7D,mBACEf,KAAA,QAAKiB,SAAS,CAAC,yBAAyB,CAAAd,QAAA,EAErCC,WAAW,eACVN,IAAA,QACEmB,SAAS,CAAC,8BAA8B,CACxCC,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCL,IAAA,QAAKmB,SAAS,CAAC,4DAA4D,CAAM,CAAC,CAC/E,CACN,cAGDnB,IAAA,QAAKmB,SAAS,yKAAAE,MAAA,CAEVf,WAAW,CAAG,eAAe,CAAG,mBAAmB,YACrD,CAAAD,QAAA,cACAH,KAAA,QAAKiB,SAAS,CAAC,sBAAsB,CAAAd,QAAA,eAEnCH,KAAA,QAAKiB,SAAS,CAAC,0FAA0F,CAAAd,QAAA,eACvGH,KAAA,QAAKiB,SAAS,CAAC,6BAA6B,CAAAd,QAAA,eAC1CL,IAAA,QAAKmB,SAAS,CAAC,iEAAiE,CAAAd,QAAA,cAC9EL,IAAA,SAAMmB,SAAS,CAAC,8BAA8B,CAAAd,QAAA,CAAC,IAAE,CAAM,CAAC,CACrD,CAAC,cACNL,IAAA,SAAMmB,SAAS,CAAC,8BAA8B,CAAAd,QAAA,CAAC,aAAW,CAAM,CAAC,EAC9D,CAAC,cACNL,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,KAAK,CAAE,CACrCY,SAAS,CAAC,oDAAoD,CAAAd,QAAA,cAE9DL,IAAA,CAACL,CAAC,EAACwB,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,EACN,CAAC,cAGNnB,IAAA,QAAKmB,SAAS,CAAC,4BAA4B,CAAAd,QAAA,CACxCK,UAAU,CAACY,GAAG,CAAEC,IAAI,EAAK,CACxB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACV,IAAI,CACtB,mBACEX,KAAA,WAEEkB,OAAO,CAAEA,CAAA,GAAM,CACbZ,QAAQ,CAACe,IAAI,CAACX,IAAI,CAAC,CACnBL,cAAc,CAAC,KAAK,CAAC,CACvB,CAAE,CACFY,SAAS,6IAAAE,MAAA,CAELL,QAAQ,CAACO,IAAI,CAACX,IAAI,CAAC,CACjB,qDAAqD,CACrD,oDAAoD,wBAExD,CAAAP,QAAA,eAEFL,IAAA,CAACwB,IAAI,EAACL,SAAS,iBAAAE,MAAA,CAAkBL,QAAQ,CAACO,IAAI,CAACX,IAAI,CAAC,CAAG,eAAe,CAAG,eAAe,CAAG,CAAE,CAAC,CAC7FW,IAAI,CAACZ,IAAI,GAdLY,IAAI,CAACZ,IAeJ,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,cAGNT,KAAA,QAAKiB,SAAS,CAAC,8BAA8B,CAAAd,QAAA,eAC3CH,KAAA,QAAKiB,SAAS,CAAC,kCAAkC,CAAAd,QAAA,eAC/CL,IAAA,QAAKmB,SAAS,CAAC,sGAAsG,CAAAd,QAAA,cACnHL,IAAA,CAACJ,IAAI,EAACuB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,gBAAgB,CAAAd,QAAA,eAC7BL,IAAA,MAAGmB,SAAS,CAAC,4CAA4C,CAAAd,QAAA,CAAC,WAAS,CAAG,CAAC,cACvEL,IAAA,MAAGmB,SAAS,CAAC,gCAAgC,CAAAd,QAAA,CAAC,kBAAgB,CAAG,CAAC,EAC/D,CAAC,EACH,CAAC,cACNH,KAAA,WACEkB,OAAO,CAAEN,YAAa,CACtBK,SAAS,CAAC,sGAAsG,CAAAd,QAAA,eAEhHL,IAAA,CAACP,MAAM,EAAC0B,SAAS,CAAC,cAAc,CAAE,CAAC,WAErC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGNjB,KAAA,QAAKiB,SAAS,CAAC,UAAU,CAAAd,QAAA,eAEvBL,IAAA,QAAKmB,SAAS,CAAC,+DAA+D,CAAAd,QAAA,cAC5EH,KAAA,QAAKiB,SAAS,CAAC,6DAA6D,CAAAd,QAAA,eAC1EL,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAMb,cAAc,CAAC,IAAI,CAAE,CACpCY,SAAS,CAAC,6CAA6C,CAAAd,QAAA,cAEvDL,IAAA,CAACN,IAAI,EAACyB,SAAS,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cAETnB,IAAA,QAAKmB,SAAS,CAAC,qCAAqC,CAAAd,QAAA,cAClDH,KAAA,WAAQiB,SAAS,CAAC,4CAA4C,CAAAd,QAAA,eAC5DL,IAAA,CAACH,IAAI,EAACsB,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BnB,IAAA,SAAMmB,SAAS,CAAC,0DAA0D,CAAO,CAAC,EAC5E,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,cAGNnB,IAAA,SAAMmB,SAAS,CAAC,QAAQ,CAAAd,QAAA,CACrBA,QAAQ,CACL,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}