/**
 * Authentication service
 * Handles user authentication, registration, and session management
 */
import baseHttp from './baseHttp';
import { User, LoginRequest, RegisterRequest } from '../types';

interface LoginResponse {
  success: boolean;
  message: string;
  user?: User;
}

class AuthService {
  private readonly AUTH_ENDPOINTS = {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me',
    CHECK: '/api/auth/check',
    UPDATE_PROFILE: '/api/auth/me',
  };

  /**
   * Login user with email and password
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await baseHttp.post<LoginResponse>(
        this.AUTH_ENDPOINTS.LOGIN,
        credentials,
        { withCredentials: true } // Include cookies
      );

      if (response.success && response.user) {
        // Store user data locally
        this.storeUserData(response.user);
      }

      return response;
    } catch (error) {
      console.error('Lo<PERSON> failed:', error);
      throw error;
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<LoginResponse> {
    try {
      const response = await baseHttp.post<LoginResponse>(
        this.AUTH_ENDPOINTS.REGISTER,
        userData,
        { withCredentials: true } // Include cookies
      );

      if (response.success && response.user) {
        // Store user data locally
        this.storeUserData(response.user);
      }

      return response;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<User> {
    try {
      return await baseHttp.get<User>(this.AUTH_ENDPOINTS.ME, {
        withCredentials: true
      });
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const response = await baseHttp.put<User>(
        this.AUTH_ENDPOINTS.UPDATE_PROFILE,
        userData
      );

      // Update stored user data
      this.updateStoredUserData(response);

      return response;
    } catch (error) {
      console.error('Profile update failed:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await baseHttp.post(this.AUTH_ENDPOINTS.LOGOUT, {}, {
        withCredentials: true
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearAuthData();
      // Redirect to login page
      window.location.href = '/auth/login';
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const response = await baseHttp.get(this.AUTH_ENDPOINTS.CHECK, {
        withCredentials: true
      });
      return response.authenticated;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if user is authenticated (sync version)
   */
  isAuthenticatedSync(): boolean {
    const user = this.getStoredUser();
    return !!user;
  }

  /**
   * Get stored user data
   */
  getStoredUser(): User | null {
    const userData = localStorage.getItem('user_data');
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (error) {
        console.error('Failed to parse stored user data:', error);
        this.clearAuthData();
        return null;
      }
    }
    return null;
  }

  /**
   * Store user data locally
   */
  private storeUserData(user: User): void {
    localStorage.setItem('user_data', JSON.stringify(user));
  }

  /**
   * Update stored user data
   */
  private updateStoredUserData(user: User): void {
    localStorage.setItem('user_data', JSON.stringify(user));
  }

  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    localStorage.removeItem('user_data');
  }

  /**
   * Initialize auth service (call on app startup)
   */
  initialize(): void {
    // No token initialization needed for session-based auth
    // Session is handled by cookies automatically
  }

  /**
   * Validate session and refresh user data
   */
  async validateAndRefreshAuth(): Promise<boolean> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) {
        this.clearAuthData();
        return false;
      }

      const user = await this.getCurrentUser();
      this.updateStoredUserData(user);
      return true;
    } catch (error) {
      console.error('Auth validation failed:', error);
      this.clearAuthData();
      return false;
    }
  }
}

// Create and export singleton instance
const authService = new AuthService();
export default authService;

// Export the class for testing purposes
export { AuthService };
