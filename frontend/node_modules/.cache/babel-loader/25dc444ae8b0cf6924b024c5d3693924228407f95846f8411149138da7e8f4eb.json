{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"4ckbqe\"\n}], [\"path\", {\n  d: \"m16 10-4.5 4.5\",\n  key: \"7p3ebg\"\n}], [\"path\", {\n  d: \"m15 11 1 1\",\n  key: \"1bsyx3\"\n}]];\nconst FileKey = createLucideIcon(\"file-key\", __iconNode);\nexport { __iconNode, FileKey as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "<PERSON><PERSON>ey", "createLucideIcon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/lucide-react/src/icons/file-key.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['circle', { cx: '10', cy: '16', r: '2', key: '4ckbqe' }],\n  ['path', { d: 'm16 10-4.5 4.5', key: '7p3ebg' }],\n  ['path', { d: 'm15 11 1 1', key: '1bsyx3' }],\n];\n\n/**\n * @component @name FileKey\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxjaXJjbGUgY3g9IjEwIiBjeT0iMTYiIHI9IjIiIC8+CiAgPHBhdGggZD0ibTE2IDEwLTQuNSA0LjUiIC8+CiAgPHBhdGggZD0ibTE1IDExIDEgMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-key\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileKey = createLucideIcon('file-key', __iconNode);\n\nexport default FileKey;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,4DAA8D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3F,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAI,OAAA,GAAUC,gBAAiB,aAAYP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}