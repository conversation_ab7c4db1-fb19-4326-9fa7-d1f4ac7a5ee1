{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/cta/CTAPage.tsx\",\n  _s = $RefreshSig$();\n/**\n * CTA (Call-to-Action) Page\n * Displays bookings and promotional content\n */\nimport React, { useState } from 'react';\nimport { Calendar, Clock, User, Phone, CheckCircle, Star, ArrowRight, Sparkles, Target } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CTAPage = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('bookings');\n  const bookings = [{\n    id: '1',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+1234567890',\n    service: 'General Consultation',\n    date: '2025-07-03',\n    time: '10:00 AM',\n    status: 'confirmed'\n  }, {\n    id: '2',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+1234567891',\n    service: 'Technical Support',\n    date: '2025-07-03',\n    time: '2:00 PM',\n    status: 'pending'\n  }, {\n    id: '3',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+1234567892',\n    service: 'Course Enrollment',\n    date: '2025-07-02',\n    time: '11:00 AM',\n    status: 'completed'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const promotions = [{\n    id: 1,\n    title: 'Premium AI Consultation',\n    description: 'Get personalized AI-powered advice for your business needs',\n    price: '$99',\n    originalPrice: '$149',\n    features: ['1-on-1 consultation', 'AI strategy planning', 'Implementation guide', '30-day support'],\n    badge: 'Most Popular'\n  }, {\n    id: 2,\n    title: 'Technical Integration',\n    description: 'Complete technical setup and integration support',\n    price: '$199',\n    originalPrice: '$299',\n    features: ['Full system setup', 'API integration', 'Custom configuration', '60-day support'],\n    badge: 'Best Value'\n  }, {\n    id: 3,\n    title: 'Enterprise Package',\n    description: 'Comprehensive solution for large organizations',\n    price: '$499',\n    originalPrice: '$799',\n    features: ['Unlimited consultations', 'Priority support', 'Custom development', '1-year warranty'],\n    badge: 'Enterprise'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 lg:p-8 space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(Target, {\n            className: \"h-8 w-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900 mb-2\",\n        children: \"CTA Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 max-w-2xl mx-auto\",\n        children: \"Manage your bookings and explore our premium services designed to accelerate your success\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-100 p-1 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('bookings'),\n          className: `px-6 py-2 rounded-md text-sm font-medium transition-all ${activeTab === 'bookings' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: \"Bookings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('promotions'),\n          className: `px-6 py-2 rounded-md text-sm font-medium transition-all ${activeTab === 'promotions' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: \"Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), activeTab === 'bookings' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Recent Bookings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"New Booking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid gap-4\",\n        children: bookings.map((booking, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-300 animate-fade-in-up\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(User, {\n                  className: \"h-5 w-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: booking.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: booking.service\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`,\n              children: booking.status.charAt(0).toUpperCase() + booking.status.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: booking.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Clock, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: booking.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: booking.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 17\n          }, this)]\n        }, booking.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Premium Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Choose the perfect plan for your needs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: promotions.map((promo, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2 animate-fade-in-up relative\",\n          style: {\n            animationDelay: `${index * 150}ms`\n          },\n          children: [promo.badge && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\",\n              children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"h-3 w-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: promo.badge\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-2\",\n              children: promo.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm mb-4\",\n              children: promo.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl font-bold text-gray-900\",\n                children: promo.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg text-gray-500 line-through\",\n                children: promo.originalPrice\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-3 mb-6\",\n            children: promo.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center space-x-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"h-4 w-4 text-green-500 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: feature\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 23\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n              className: \"h-4 w-4 group-hover:translate-x-1 transition-transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 17\n          }, this)]\n        }, promo.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-4\",\n          children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(Star, {\n            className: \"h-5 w-5 text-yellow-400 fill-current\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"blockquote\", {\n          className: \"text-lg text-gray-700 mb-4\",\n          children: \"\\\"The AI consultation service transformed our business operations. Highly recommended!\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"cite\", {\n          className: \"text-sm text-gray-600\",\n          children: \"- Sarah Johnson, CEO at TechCorp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(CTAPage, \"Ap9Et+1RxSMikjmPGvpxhZUtwgY=\");\n_c = CTAPage;\nexport default CTAPage;\nvar _c;\n$RefreshReg$(_c, \"CTAPage\");", "map": {"version": 3, "names": ["React", "useState", "Calendar", "Clock", "User", "Phone", "CheckCircle", "Star", "ArrowRight", "<PERSON><PERSON><PERSON>", "Target", "jsxDEV", "_jsxDEV", "CTAPage", "_s", "activeTab", "setActiveTab", "bookings", "id", "name", "email", "phone", "service", "date", "time", "status", "getStatusColor", "promotions", "title", "description", "price", "originalPrice", "features", "badge", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "booking", "index", "style", "animationDelay", "char<PERSON>t", "toUpperCase", "slice", "promo", "feature", "idx", "Array", "_", "i", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/cta/CTAPage.tsx"], "sourcesContent": ["/**\n * CTA (Call-to-Action) Page\n * Displays bookings and promotional content\n */\nimport React, { useState } from 'react';\nimport { \n  Calendar, \n  Clock, \n  User, \n  Phone, \n  Mail, \n  CheckCircle,\n  Star,\n  ArrowRight,\n  Sparkles,\n  Target\n} from 'lucide-react';\n\ninterface Booking {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  service: string;\n  date: string;\n  time: string;\n  status: 'confirmed' | 'pending' | 'completed';\n}\n\nconst CTAPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'bookings' | 'promotions'>('bookings');\n\n  const bookings: Booking[] = [\n    {\n      id: '1',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      phone: '+1234567890',\n      service: 'General Consultation',\n      date: '2025-07-03',\n      time: '10:00 AM',\n      status: 'confirmed'\n    },\n    {\n      id: '2',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      phone: '+1234567891',\n      service: 'Technical Support',\n      date: '2025-07-03',\n      time: '2:00 PM',\n      status: 'pending'\n    },\n    {\n      id: '3',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      phone: '+1234567892',\n      service: 'Course Enrollment',\n      date: '2025-07-02',\n      time: '11:00 AM',\n      status: 'completed'\n    }\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'confirmed': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'completed': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const promotions = [\n    {\n      id: 1,\n      title: 'Premium AI Consultation',\n      description: 'Get personalized AI-powered advice for your business needs',\n      price: '$99',\n      originalPrice: '$149',\n      features: ['1-on-1 consultation', 'AI strategy planning', 'Implementation guide', '30-day support'],\n      badge: 'Most Popular'\n    },\n    {\n      id: 2,\n      title: 'Technical Integration',\n      description: 'Complete technical setup and integration support',\n      price: '$199',\n      originalPrice: '$299',\n      features: ['Full system setup', 'API integration', 'Custom configuration', '60-day support'],\n      badge: 'Best Value'\n    },\n    {\n      id: 3,\n      title: 'Enterprise Package',\n      description: 'Comprehensive solution for large organizations',\n      price: '$499',\n      originalPrice: '$799',\n      features: ['Unlimited consultations', 'Priority support', 'Custom development', '1-year warranty'],\n      badge: 'Enterprise'\n    }\n  ];\n\n  return (\n    <div className=\"p-6 lg:p-8 space-y-8\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <div className=\"flex items-center justify-center mb-4\">\n          <div className=\"p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full\">\n            <Target className=\"h-8 w-8 text-white\" />\n          </div>\n        </div>\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">CTA Dashboard</h1>\n        <p className=\"text-gray-600 max-w-2xl mx-auto\">\n          Manage your bookings and explore our premium services designed to accelerate your success\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"flex justify-center\">\n        <div className=\"bg-gray-100 p-1 rounded-lg\">\n          <button\n            onClick={() => setActiveTab('bookings')}\n            className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${\n              activeTab === 'bookings'\n                ? 'bg-white text-gray-900 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Bookings\n          </button>\n          <button\n            onClick={() => setActiveTab('promotions')}\n            className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${\n              activeTab === 'promotions'\n                ? 'bg-white text-gray-900 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Promotions\n          </button>\n        </div>\n      </div>\n\n      {/* Content */}\n      {activeTab === 'bookings' ? (\n        <div className=\"space-y-6\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">Recent Bookings</h2>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n              New Booking\n            </button>\n          </div>\n\n          <div className=\"grid gap-4\">\n            {bookings.map((booking, index) => (\n              <div\n                key={booking.id}\n                className=\"bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-300 animate-fade-in-up\"\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\">\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-900\">{booking.name}</h3>\n                      <p className=\"text-sm text-gray-600\">{booking.service}</p>\n                    </div>\n                  </div>\n                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>\n                    {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                  <div className=\"flex items-center space-x-2 text-gray-600\">\n                    <Calendar className=\"h-4 w-4\" />\n                    <span>{booking.date}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2 text-gray-600\">\n                    <Clock className=\"h-4 w-4\" />\n                    <span>{booking.time}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2 text-gray-600\">\n                    <Phone className=\"h-4 w-4\" />\n                    <span>{booking.phone}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Premium Services</h2>\n            <p className=\"text-gray-600\">Choose the perfect plan for your needs</p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {promotions.map((promo, index) => (\n              <div\n                key={promo.id}\n                className=\"bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2 animate-fade-in-up relative\"\n                style={{ animationDelay: `${index * 150}ms` }}\n              >\n                {promo.badge && (\n                  <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                    <span className=\"bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\">\n                      <Sparkles className=\"h-3 w-3\" />\n                      <span>{promo.badge}</span>\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"text-center mb-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{promo.title}</h3>\n                  <p className=\"text-gray-600 text-sm mb-4\">{promo.description}</p>\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    <span className=\"text-3xl font-bold text-gray-900\">{promo.price}</span>\n                    <span className=\"text-lg text-gray-500 line-through\">{promo.originalPrice}</span>\n                  </div>\n                </div>\n\n                <ul className=\"space-y-3 mb-6\">\n                  {promo.features.map((feature, idx) => (\n                    <li key={idx} className=\"flex items-center space-x-2 text-sm\">\n                      <CheckCircle className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-gray-700\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n\n                <button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group\">\n                  <span>Get Started</span>\n                  <ArrowRight className=\"h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                </button>\n              </div>\n            ))}\n          </div>\n\n          {/* Testimonial Section */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n              ))}\n            </div>\n            <blockquote className=\"text-lg text-gray-700 mb-4\">\n              \"The AI consultation service transformed our business operations. Highly recommended!\"\n            </blockquote>\n            <cite className=\"text-sm text-gray-600\">- Sarah Johnson, CEO at TechCorp</cite>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CTAPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,KAAK,EAELC,WAAW,EACXC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAatB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAA4B,UAAU,CAAC;EAEjF,MAAMgB,QAAmB,GAAG,CAC1B;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,sBAAsB;IAC/BC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,mBAAmB;IAC5BC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,mBAAmB;IAC5BC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,cAAc,GAAID,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,6BAA6B;MACtD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAME,UAAU,GAAG,CACjB;IACET,EAAE,EAAE,CAAC;IACLU,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;IACnGC,KAAK,EAAE;EACT,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLU,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,kDAAkD;IAC/DC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;IAC5FC,KAAK,EAAE;EACT,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLU,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,gDAAgD;IAC7DC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;IAClGC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACErB,OAAA;IAAKsB,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBAEnCvB,OAAA;MAAKsB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvB,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDvB,OAAA;UAAKsB,SAAS,EAAC,8DAA8D;UAAAC,QAAA,eAC3EvB,OAAA,CAACF,MAAM;YAACwB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3B,OAAA;QAAIsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE3B,OAAA;QAAGsB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCvB,OAAA;QAAKsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCvB,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,UAAU,CAAE;UACxCkB,SAAS,EAAE,2DACTnB,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoB,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,YAAY,CAAE;UAC1CkB,SAAS,EAAE,2DACTnB,SAAS,KAAK,YAAY,GACtB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoB,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxB,SAAS,KAAK,UAAU,gBACvBH,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvB,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDvB,OAAA;UAAIsB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxE3B,OAAA;UAAQsB,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAAC;QAEpG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN3B,OAAA;QAAKsB,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBlB,QAAQ,CAACwB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B/B,OAAA;UAEEsB,SAAS,EAAC,+GAA+G;UACzHU,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAAR,QAAA,gBAE9CvB,OAAA;YAAKsB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDvB,OAAA;cAAKsB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvB,OAAA;gBAAKsB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,eACnHvB,OAAA,CAACR,IAAI;kBAAC8B,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACN3B,OAAA;gBAAAuB,QAAA,gBACEvB,OAAA;kBAAIsB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEO,OAAO,CAACvB;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/D3B,OAAA;kBAAGsB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEO,OAAO,CAACpB;gBAAO;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3B,OAAA;cAAMsB,SAAS,EAAE,8CAA8CR,cAAc,CAACgB,OAAO,CAACjB,MAAM,CAAC,EAAG;cAAAU,QAAA,EAC7FO,OAAO,CAACjB,MAAM,CAACqB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGL,OAAO,CAACjB,MAAM,CAACuB,KAAK,CAAC,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN3B,OAAA;YAAKsB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DvB,OAAA;cAAKsB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDvB,OAAA,CAACV,QAAQ;gBAACgC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC3B,OAAA;gBAAAuB,QAAA,EAAOO,OAAO,CAACnB;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACN3B,OAAA;cAAKsB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDvB,OAAA,CAACT,KAAK;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7B3B,OAAA;gBAAAuB,QAAA,EAAOO,OAAO,CAAClB;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACN3B,OAAA;cAAKsB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDvB,OAAA,CAACP,KAAK;gBAAC6B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7B3B,OAAA;gBAAAuB,QAAA,EAAOO,OAAO,CAACrB;cAAK;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhCDG,OAAO,CAACxB,EAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiCZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN3B,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvB,OAAA;QAAKsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvB,OAAA;UAAIsB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E3B,OAAA;UAAGsB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eAEN3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDR,UAAU,CAACc,GAAG,CAAC,CAACQ,KAAK,EAAEN,KAAK,kBAC3B/B,OAAA;UAEEsB,SAAS,EAAC,uJAAuJ;UACjKU,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAAR,QAAA,GAE7Cc,KAAK,CAAChB,KAAK,iBACVrB,OAAA;YAAKsB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEvB,OAAA;cAAMsB,SAAS,EAAC,+HAA+H;cAAAC,QAAA,gBAC7IvB,OAAA,CAACH,QAAQ;gBAACyB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC3B,OAAA;gBAAAuB,QAAA,EAAOc,KAAK,CAAChB;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eAED3B,OAAA;YAAKsB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvB,OAAA;cAAIsB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAEc,KAAK,CAACrB;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvE3B,OAAA;cAAGsB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEc,KAAK,CAACpB;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjE3B,OAAA;cAAKsB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDvB,OAAA;gBAAMsB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEc,KAAK,CAACnB;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvE3B,OAAA;gBAAMsB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAEc,KAAK,CAAClB;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3B,OAAA;YAAIsB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3Bc,KAAK,CAACjB,QAAQ,CAACS,GAAG,CAAC,CAACS,OAAO,EAAEC,GAAG,kBAC/BvC,OAAA;cAAcsB,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBAC3DvB,OAAA,CAACN,WAAW;gBAAC4B,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChE3B,OAAA;gBAAMsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEe;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAFzCY,GAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGR,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEL3B,OAAA;YAAQsB,SAAS,EAAC,qMAAqM;YAAAC,QAAA,gBACrNvB,OAAA;cAAAuB,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxB3B,OAAA,CAACJ,UAAU;cAAC0B,SAAS,EAAC;YAAwD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA,GAlCJU,KAAK,CAAC/B,EAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3B,OAAA;QAAKsB,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpFvB,OAAA;UAAKsB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtC,CAAC,GAAGiB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,kBACtB1C,OAAA,CAACL,IAAI;YAAS2B,SAAS,EAAC;UAAsC,GAAnDoB,CAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoD,CACjE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3B,OAAA;UAAYsB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3B,OAAA;UAAMsB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzB,EAAA,CAvOID,OAAiB;AAAA0C,EAAA,GAAjB1C,OAAiB;AAyOvB,eAAeA,OAAO;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}