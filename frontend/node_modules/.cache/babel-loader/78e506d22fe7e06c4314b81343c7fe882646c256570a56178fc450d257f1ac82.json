{"ast": null, "code": "/**\n * Public Layout Component\n * Used for authentication pages (login, signup) - no sidebar\n */import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PublicLayout=_ref=>{let{children}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-grid-pattern opacity-5\"}),/*#__PURE__*/_jsx(\"header\",{className:\"relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center h-16\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:\"CS\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-gray-900\",children:\"Chat System\"})]}),/*#__PURE__*/_jsxs(\"nav\",{className:\"hidden md:flex items-center space-x-8\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"#features\",className:\"text-gray-600 hover:text-gray-900 transition-colors\",children:\"Features\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#about\",className:\"text-gray-600 hover:text-gray-900 transition-colors\",children:\"About\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#contact\",className:\"text-gray-600 hover:text-gray-900 transition-colors\",children:\"Contact\"})]})]})})}),/*#__PURE__*/_jsx(\"main\",{className:\"relative z-10 flex-1\",children:children}),/*#__PURE__*/_jsx(\"footer\",{className:\"relative z-10 bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-auto\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-gray-600\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2025 Chat System. All rights reserved.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm\",children:\"Built with \\u2764\\uFE0F using React, TypeScript, and Tailwind CSS\"})]})})})]});};export default PublicLayout;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "PublicLayout", "_ref", "children", "className", "href"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/PublicLayout.tsx"], "sourcesContent": ["/**\n * Public Layout Component\n * Used for authentication pages (login, signup) - no sidebar\n */\nimport React from 'react';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nconst PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      \n      {/* Header */}\n      <header className=\"relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">CS</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Chat System</span>\n            </div>\n            \n            {/* Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#features\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                Features\n              </a>\n              <a href=\"#about\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                About\n              </a>\n              <a href=\"#contact\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                Contact\n              </a>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"relative z-10 flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"relative z-10 bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-auto\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>&copy; 2025 Chat System. All rights reserved.</p>\n            <p className=\"mt-2 text-sm\">\n              Built with ❤️ using React, TypeScript, and Tailwind CSS\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default PublicLayout;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM1B,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC7D,mBACEF,KAAA,QAAKI,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eAEjFL,IAAA,QAAKM,SAAS,CAAC,4CAA4C,CAAM,CAAC,cAGlEN,IAAA,WAAQM,SAAS,CAAC,qEAAqE,CAAAD,QAAA,cACrFL,IAAA,QAAKM,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrDH,KAAA,QAAKI,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAErDH,KAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CL,IAAA,QAAKM,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/GL,IAAA,SAAMM,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,IAAE,CAAM,CAAC,CACrD,CAAC,cACNL,IAAA,SAAMM,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,EACjE,CAAC,cAGNH,KAAA,QAAKI,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDL,IAAA,MAAGO,IAAI,CAAC,WAAW,CAACD,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpF,CAAG,CAAC,cACJL,IAAA,MAAGO,IAAI,CAAC,QAAQ,CAACD,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,OAEjF,CAAG,CAAC,cACJL,IAAA,MAAGO,IAAI,CAAC,UAAU,CAACD,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,SAEnF,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,CACA,CAAC,cAGTL,IAAA,SAAMM,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CACnCA,QAAQ,CACL,CAAC,cAGPL,IAAA,WAAQM,SAAS,CAAC,6EAA6E,CAAAD,QAAA,cAC7FL,IAAA,QAAKM,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1DH,KAAA,QAAKI,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCL,IAAA,MAAAK,QAAA,CAAG,6CAA6C,CAAG,CAAC,cACpDL,IAAA,MAAGM,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,mEAE5B,CAAG,CAAC,EACD,CAAC,CACH,CAAC,CACA,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}