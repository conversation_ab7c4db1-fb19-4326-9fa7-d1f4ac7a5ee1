{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport PlaygroundPage from './pages/playground/PlaygroundPage';\nimport CTAPage from './pages/cta/CTAPage';\nimport { PublicLayout, ProtectedLayout } from './layouts';\nimport { authService } from './services';\n\n// Protected Route Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/auth/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Public Route Component (redirect to dashboard if already authenticated)\n_c = ProtectedRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(PublicLayout, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 10\n  }, this);\n};\n\n// Protected Route Component with Layout\n_c2 = PublicRoute;\nconst ProtectedRouteWithLayout = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/auth/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ProtectedLayout, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 10\n  }, this);\n};\n_c3 = ProtectedRouteWithLayout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/auth/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n          children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/signup\",\n        element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n          children: /*#__PURE__*/_jsxDEV(SignupPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRouteWithLayout, {\n          children: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/playground\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRouteWithLayout, {\n          children: /*#__PURE__*/_jsxDEV(PlaygroundPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cta\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRouteWithLayout, {\n          children: /*#__PURE__*/_jsxDEV(CTAPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/auth/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"ProtectedRouteWithLayout\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginPage", "SignupPage", "DashboardPage", "PlaygroundPage", "CTAPage", "PublicLayout", "ProtectedLayout", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "isAuthenticated", "isAuthenticatedSync", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PublicRoute", "_c2", "ProtectedRouteWithLayout", "_c3", "App", "path", "element", "_c4", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport PlaygroundPage from './pages/playground/PlaygroundPage';\nimport CTAPage from './pages/cta/CTAPage';\nimport { PublicLayout, ProtectedLayout } from './layouts';\nimport { authService } from './services';\n\n// Protected Route Component\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Public Route Component (redirect to dashboard if already authenticated)\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <PublicLayout>{children}</PublicLayout>;\n};\n\n// Protected Route Component with Layout\nconst ProtectedRouteWithLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  return <ProtectedLayout>{children}</ProtectedLayout>;\n};\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        {/* Default route - redirect to login */}\n        <Route path=\"/\" element={<Navigate to=\"/auth/login\" replace />} />\n\n        {/* Auth routes */}\n        <Route\n          path=\"/auth/login\"\n          element={\n            <PublicRoute>\n              <LoginPage />\n            </PublicRoute>\n          }\n        />\n        <Route\n          path=\"/auth/signup\"\n          element={\n            <PublicRoute>\n              <SignupPage />\n            </PublicRoute>\n          }\n        />\n\n        {/* Protected routes */}\n        <Route\n          path=\"/dashboard\"\n          element={\n            <ProtectedRouteWithLayout>\n              <DashboardPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n        <Route\n          path=\"/playground\"\n          element={\n            <ProtectedRouteWithLayout>\n              <PlaygroundPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n        <Route\n          path=\"/cta\"\n          element={\n            <ProtectedRouteWithLayout>\n              <CTAPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n\n        {/* Catch all route - redirect to login */}\n        <Route path=\"*\" element={<Navigate to=\"/auth/login\" replace />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,WAAW;AACzD,SAASC,WAAW,QAAQ,YAAY;;AAExC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAChF,MAAMC,eAAe,GAAGP,WAAW,CAACQ,mBAAmB,CAAC,CAAC;EAEzD,IAAI,CAACD,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACV,QAAQ;MAACiB,EAAE,EAAC,aAAa;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9C;EAEA,oBAAOZ,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAS,EAAA,GAVMV,cAAuD;AAW7D,MAAMW,WAAoD,GAAGA,CAAC;EAAEV;AAAS,CAAC,KAAK;EAC7E,MAAMC,eAAe,GAAGP,WAAW,CAACQ,mBAAmB,CAAC,CAAC;EAEzD,IAAID,eAAe,EAAE;IACnB,oBAAOL,OAAA,CAACV,QAAQ;MAACiB,EAAE,EAAC,YAAY;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C;EAEA,oBAAOZ,OAAA,CAACJ,YAAY;IAAAQ,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAe,CAAC;AAChD,CAAC;;AAED;AAAAG,GAAA,GAVMD,WAAoD;AAW1D,MAAME,wBAAiE,GAAGA,CAAC;EAAEZ;AAAS,CAAC,KAAK;EAC1F,MAAMC,eAAe,GAAGP,WAAW,CAACQ,mBAAmB,CAAC,CAAC;EAEzD,IAAI,CAACD,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACV,QAAQ;MAACiB,EAAE,EAAC,aAAa;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9C;EAEA,oBAAOZ,OAAA,CAACH,eAAe;IAAAO,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAkB,CAAC;AACtD,CAAC;AAACK,GAAA,GARID,wBAAiE;AAUvE,SAASE,GAAGA,CAAA,EAAG;EACb,oBACElB,OAAA,CAACb,MAAM;IAAAiB,QAAA,eACLJ,OAAA,CAACZ,MAAM;MAAAgB,QAAA,gBAELJ,OAAA,CAACX,KAAK;QAAC8B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEpB,OAAA,CAACV,QAAQ;UAACiB,EAAE,EAAC,aAAa;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlEZ,OAAA,CAACX,KAAK;QACJ8B,IAAI,EAAC,aAAa;QAClBC,OAAO,eACLpB,OAAA,CAACc,WAAW;UAAAV,QAAA,eACVJ,OAAA,CAACT,SAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACd;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFZ,OAAA,CAACX,KAAK;QACJ8B,IAAI,EAAC,cAAc;QACnBC,OAAO,eACLpB,OAAA,CAACc,WAAW;UAAAV,QAAA,eACVJ,OAAA,CAACR,UAAU;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACd;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFZ,OAAA,CAACX,KAAK;QACJ8B,IAAI,EAAC,YAAY;QACjBC,OAAO,eACLpB,OAAA,CAACgB,wBAAwB;UAAAZ,QAAA,eACvBJ,OAAA,CAACP,aAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAC3B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFZ,OAAA,CAACX,KAAK;QACJ8B,IAAI,EAAC,aAAa;QAClBC,OAAO,eACLpB,OAAA,CAACgB,wBAAwB;UAAAZ,QAAA,eACvBJ,OAAA,CAACN,cAAc;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAC3B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFZ,OAAA,CAACX,KAAK;QACJ8B,IAAI,EAAC,MAAM;QACXC,OAAO,eACLpB,OAAA,CAACgB,wBAAwB;UAAAZ,QAAA,eACvBJ,OAAA,CAACL,OAAO;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAC3B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFZ,OAAA,CAACX,KAAK;QAAC8B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEpB,OAAA,CAACV,QAAQ;UAACiB,EAAE,EAAC,aAAa;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACS,GAAA,GAxDQH,GAAG;AA0DZ,eAAeA,GAAG;AAAC,IAAAL,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}