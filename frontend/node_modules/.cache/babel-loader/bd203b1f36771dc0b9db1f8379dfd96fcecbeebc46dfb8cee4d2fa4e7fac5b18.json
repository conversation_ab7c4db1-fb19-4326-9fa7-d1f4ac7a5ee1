{"ast": null, "code": "/**\n * Authentication service\n * Handles user authentication, registration, and session management\n */import baseHttp from'./baseHttp';class AuthService{constructor(){this.AUTH_ENDPOINTS={LOGIN:'/api/auth/login',REGISTER:'/api/auth/register',LOGOUT:'/api/auth/logout',ME:'/api/auth/me',CHECK:'/api/auth/check',UPDATE_PROFILE:'/api/auth/me'};}/**\n   * Login user with email and password\n   */async login(credentials){try{const response=await baseHttp.post(this.AUTH_ENDPOINTS.LOGIN,credentials,{withCredentials:true}// Include cookies\n);if(response.success&&response.user){// Store user data locally\nthis.storeUserData(response.user);}return response;}catch(error){console.error('Login failed:',error);throw error;}}/**\n   * Register new user\n   */async register(userData){try{const response=await baseHttp.post(this.AUTH_ENDPOINTS.REGISTER,userData,{withCredentials:true}// Include cookies\n);if(response.success&&response.user){// Store user data locally\nthis.storeUserData(response.user);}return response;}catch(error){console.error('Registration failed:',error);throw error;}}/**\n   * Get current user information\n   */async getCurrentUser(){try{return await baseHttp.get(this.AUTH_ENDPOINTS.ME,{withCredentials:true});}catch(error){console.error('Failed to get current user:',error);throw error;}}/**\n   * Update user profile\n   */async updateProfile(userData){try{const response=await baseHttp.put(this.AUTH_ENDPOINTS.UPDATE_PROFILE,userData);// Update stored user data\nthis.updateStoredUserData(response);return response;}catch(error){console.error('Profile update failed:',error);throw error;}}/**\n   * Logout user\n   */async logout(){try{await baseHttp.post(this.AUTH_ENDPOINTS.LOGOUT,{},{withCredentials:true});}catch(error){console.error('Logout request failed:',error);}finally{this.clearAuthData();// Redirect to login page\nwindow.location.href='/auth/login';}}/**\n   * Check if user is authenticated\n   */async isAuthenticated(){try{const response=await baseHttp.get(this.AUTH_ENDPOINTS.CHECK,{withCredentials:true});return response.authenticated;}catch(error){return false;}}/**\n   * Check if user is authenticated (sync version)\n   */isAuthenticatedSync(){const user=this.getStoredUser();return!!user;}/**\n   * Get stored user data\n   */getStoredUser(){const userData=localStorage.getItem('user_data');if(userData){try{return JSON.parse(userData);}catch(error){console.error('Failed to parse stored user data:',error);this.clearAuthData();return null;}}return null;}/**\n   * Store user data locally\n   */storeUserData(user){localStorage.setItem('user_data',JSON.stringify(user));}/**\n   * Update stored user data\n   */updateStoredUserData(user){localStorage.setItem('user_data',JSON.stringify(user));}/**\n   * Clear all authentication data\n   */clearAuthData(){localStorage.removeItem('user_data');}/**\n   * Initialize auth service (call on app startup)\n   */initialize(){// No token initialization needed for session-based auth\n// Session is handled by cookies automatically\n}/**\n   * Validate session and refresh user data\n   */async validateAndRefreshAuth(){try{const isAuth=await this.isAuthenticated();if(!isAuth){this.clearAuthData();return false;}const user=await this.getCurrentUser();this.updateStoredUserData(user);return true;}catch(error){console.error('Auth validation failed:',error);this.clearAuthData();return false;}}}// Create and export singleton instance\nconst authService=new AuthService();export default authService;// Export the class for testing purposes\nexport{AuthService};", "map": {"version": 3, "names": ["baseHttp", "AuthService", "constructor", "AUTH_ENDPOINTS", "LOGIN", "REGISTER", "LOGOUT", "ME", "CHECK", "UPDATE_PROFILE", "login", "credentials", "response", "post", "withCredentials", "success", "user", "storeUserData", "error", "console", "register", "userData", "getCurrentUser", "get", "updateProfile", "put", "updateStoredUserData", "logout", "clearAuthData", "window", "location", "href", "isAuthenticated", "authenticated", "isAuthenticatedSync", "getStoredUser", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "removeItem", "initialize", "validateAndRefreshAuth", "isAuth", "authService"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts"], "sourcesContent": ["/**\n * Authentication service\n * Handles user authentication, registration, and session management\n */\nimport baseHttp from './baseHttp';\nimport { User, LoginRequest, RegisterRequest } from '../types';\n\ninterface LoginResponse {\n  success: boolean;\n  message: string;\n  user?: User;\n}\n\nclass AuthService {\n  private readonly AUTH_ENDPOINTS = {\n    LOGIN: '/api/auth/login',\n    REGISTER: '/api/auth/register',\n    LOGOUT: '/api/auth/logout',\n    ME: '/api/auth/me',\n    CHECK: '/api/auth/check',\n    UPDATE_PROFILE: '/api/auth/me',\n  };\n\n  /**\n   * Login user with email and password\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await baseHttp.post<LoginResponse>(\n        this.AUTH_ENDPOINTS.LOGIN,\n        credentials,\n        { withCredentials: true } // Include cookies\n      );\n\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n\n      return response;\n    } catch (error) {\n      console.error('Lo<PERSON> failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Register new user\n   */\n  async register(userData: RegisterRequest): Promise<LoginResponse> {\n    try {\n      const response = await baseHttp.post<LoginResponse>(\n        this.AUTH_ENDPOINTS.REGISTER,\n        userData,\n        { withCredentials: true } // Include cookies\n      );\n\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n\n      return response;\n    } catch (error) {\n      console.error('Registration failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current user information\n   */\n  async getCurrentUser(): Promise<User> {\n    try {\n      return await baseHttp.get<User>(this.AUTH_ENDPOINTS.ME, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Failed to get current user:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update user profile\n   */\n  async updateProfile(userData: Partial<User>): Promise<User> {\n    try {\n      const response = await baseHttp.put<User>(\n        this.AUTH_ENDPOINTS.UPDATE_PROFILE,\n        userData\n      );\n\n      // Update stored user data\n      this.updateStoredUserData(response);\n\n      return response;\n    } catch (error) {\n      console.error('Profile update failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await baseHttp.post(this.AUTH_ENDPOINTS.LOGOUT, {}, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      this.clearAuthData();\n      // Redirect to login page\n      window.location.href = '/auth/login';\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  async isAuthenticated(): Promise<boolean> {\n    try {\n      const response = await baseHttp.get(this.AUTH_ENDPOINTS.CHECK, {\n        withCredentials: true\n      });\n      return response.authenticated;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Check if user is authenticated (sync version)\n   */\n  isAuthenticatedSync(): boolean {\n    const user = this.getStoredUser();\n    return !!user;\n  }\n\n  /**\n   * Get stored user data\n   */\n  getStoredUser(): User | null {\n    const userData = localStorage.getItem('user_data');\n    if (userData) {\n      try {\n        return JSON.parse(userData);\n      } catch (error) {\n        console.error('Failed to parse stored user data:', error);\n        this.clearAuthData();\n        return null;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Store user data locally\n   */\n  private storeUserData(user: User): void {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Update stored user data\n   */\n  private updateStoredUserData(user: User): void {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Clear all authentication data\n   */\n  private clearAuthData(): void {\n    localStorage.removeItem('user_data');\n  }\n\n  /**\n   * Initialize auth service (call on app startup)\n   */\n  initialize(): void {\n    // No token initialization needed for session-based auth\n    // Session is handled by cookies automatically\n  }\n\n  /**\n   * Validate session and refresh user data\n   */\n  async validateAndRefreshAuth(): Promise<boolean> {\n    try {\n      const isAuth = await this.isAuthenticated();\n      if (!isAuth) {\n        this.clearAuthData();\n        return false;\n      }\n\n      const user = await this.getCurrentUser();\n      this.updateStoredUserData(user);\n      return true;\n    } catch (error) {\n      console.error('Auth validation failed:', error);\n      this.clearAuthData();\n      return false;\n    }\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;\n\n// Export the class for testing purposes\nexport { AuthService };\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,QAAQ,KAAM,YAAY,CASjC,KAAM,CAAAC,WAAY,CAAAC,YAAA,OACCC,cAAc,CAAG,CAChCC,KAAK,CAAE,iBAAiB,CACxBC,QAAQ,CAAE,oBAAoB,CAC9BC,MAAM,CAAE,kBAAkB,CAC1BC,EAAE,CAAE,cAAc,CAClBC,KAAK,CAAE,iBAAiB,CACxBC,cAAc,CAAE,cAClB,CAAC,EAED;AACF;AACA,KACE,KAAM,CAAAC,KAAKA,CAACC,WAAyB,CAA0B,CAC7D,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAZ,QAAQ,CAACa,IAAI,CAClC,IAAI,CAACV,cAAc,CAACC,KAAK,CACzBO,WAAW,CACX,CAAEG,eAAe,CAAE,IAAK,CAAE;AAC5B,CAAC,CAED,GAAIF,QAAQ,CAACG,OAAO,EAAIH,QAAQ,CAACI,IAAI,CAAE,CACrC;AACA,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACI,IAAI,CAAC,CACnC,CAEA,MAAO,CAAAJ,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAE,QAAQA,CAACC,QAAyB,CAA0B,CAChE,GAAI,CACF,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAZ,QAAQ,CAACa,IAAI,CAClC,IAAI,CAACV,cAAc,CAACE,QAAQ,CAC5BgB,QAAQ,CACR,CAAEP,eAAe,CAAE,IAAK,CAAE;AAC5B,CAAC,CAED,GAAIF,QAAQ,CAACG,OAAO,EAAIH,QAAQ,CAACI,IAAI,CAAE,CACrC;AACA,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACI,IAAI,CAAC,CACnC,CAEA,MAAO,CAAAJ,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAI,cAAcA,CAAA,CAAkB,CACpC,GAAI,CACF,MAAO,MAAM,CAAAtB,QAAQ,CAACuB,GAAG,CAAO,IAAI,CAACpB,cAAc,CAACI,EAAE,CAAE,CACtDO,eAAe,CAAE,IACnB,CAAC,CAAC,CACJ,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAM,aAAaA,CAACH,QAAuB,CAAiB,CAC1D,GAAI,CACF,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAZ,QAAQ,CAACyB,GAAG,CACjC,IAAI,CAACtB,cAAc,CAACM,cAAc,CAClCY,QACF,CAAC,CAED;AACA,IAAI,CAACK,oBAAoB,CAACd,QAAQ,CAAC,CAEnC,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAS,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAA3B,QAAQ,CAACa,IAAI,CAAC,IAAI,CAACV,cAAc,CAACG,MAAM,CAAE,CAAC,CAAC,CAAE,CAClDQ,eAAe,CAAE,IACnB,CAAC,CAAC,CACJ,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACR,IAAI,CAACU,aAAa,CAAC,CAAC,CACpB;AACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,aAAa,CACtC,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAC,eAAeA,CAAA,CAAqB,CACxC,GAAI,CACF,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAZ,QAAQ,CAACuB,GAAG,CAAC,IAAI,CAACpB,cAAc,CAACK,KAAK,CAAE,CAC7DM,eAAe,CAAE,IACnB,CAAC,CAAC,CACF,MAAO,CAAAF,QAAQ,CAACqB,aAAa,CAC/B,CAAE,MAAOf,KAAK,CAAE,CACd,MAAO,MAAK,CACd,CACF,CAEA;AACF;AACA,KACEgB,mBAAmBA,CAAA,CAAY,CAC7B,KAAM,CAAAlB,IAAI,CAAG,IAAI,CAACmB,aAAa,CAAC,CAAC,CACjC,MAAO,CAAC,CAACnB,IAAI,CACf,CAEA;AACF;AACA,KACEmB,aAAaA,CAAA,CAAgB,CAC3B,KAAM,CAAAd,QAAQ,CAAGe,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAClD,GAAIhB,QAAQ,CAAE,CACZ,GAAI,CACF,MAAO,CAAAiB,IAAI,CAACC,KAAK,CAAClB,QAAQ,CAAC,CAC7B,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,IAAI,CAACU,aAAa,CAAC,CAAC,CACpB,MAAO,KAAI,CACb,CACF,CACA,MAAO,KAAI,CACb,CAEA;AACF;AACA,KACUX,aAAaA,CAACD,IAAU,CAAQ,CACtCoB,YAAY,CAACI,OAAO,CAAC,WAAW,CAAEF,IAAI,CAACG,SAAS,CAACzB,IAAI,CAAC,CAAC,CACzD,CAEA;AACF;AACA,KACUU,oBAAoBA,CAACV,IAAU,CAAQ,CAC7CoB,YAAY,CAACI,OAAO,CAAC,WAAW,CAAEF,IAAI,CAACG,SAAS,CAACzB,IAAI,CAAC,CAAC,CACzD,CAEA;AACF;AACA,KACUY,aAAaA,CAAA,CAAS,CAC5BQ,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC,CACtC,CAEA;AACF;AACA,KACEC,UAAUA,CAAA,CAAS,CACjB;AACA;AAAA,CAGF;AACF;AACA,KACE,KAAM,CAAAC,sBAAsBA,CAAA,CAAqB,CAC/C,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,KAAI,CAACb,eAAe,CAAC,CAAC,CAC3C,GAAI,CAACa,MAAM,CAAE,CACX,IAAI,CAACjB,aAAa,CAAC,CAAC,CACpB,MAAO,MAAK,CACd,CAEA,KAAM,CAAAZ,IAAI,CAAG,KAAM,KAAI,CAACM,cAAc,CAAC,CAAC,CACxC,IAAI,CAACI,oBAAoB,CAACV,IAAI,CAAC,CAC/B,MAAO,KAAI,CACb,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,IAAI,CAACU,aAAa,CAAC,CAAC,CACpB,MAAO,MAAK,CACd,CACF,CACF,CAEA;AACA,KAAM,CAAAkB,WAAW,CAAG,GAAI,CAAA7C,WAAW,CAAC,CAAC,CACrC,cAAe,CAAA6C,WAAW,CAE1B;AACA,OAAS7C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}