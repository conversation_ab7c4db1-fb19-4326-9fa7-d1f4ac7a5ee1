import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from './pages/auth/pages/auth.login';
import SignupPage from './pages/auth/pages/auth.signup';
import DashboardPage from './pages/dashboard/DashboardPage';
import PlaygroundPage from './pages/playground/PlaygroundPage';
import CTAPage from './pages/cta/CTAPage';
import { PublicLayout, ProtectedLayout } from './layouts';
import { authService } from './services';

// Public Route Component (redirect to dashboard if already authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticatedSync();

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <PublicLayout>{children}</PublicLayout>;
};

// Protected Route Component with Layout
const ProtectedRouteWithLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticatedSync();

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  return <ProtectedLayout>{children}</ProtectedLayout>;
};

function App() {
  return (
    <Router>
      <Routes>
        {/* Default route - redirect to login */}
        <Route path="/" element={<Navigate to="/auth/login" replace />} />

        {/* Auth routes */}
        <Route
          path="/auth/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/auth/signup"
          element={
            <PublicRoute>
              <SignupPage />
            </PublicRoute>
          }
        />

        {/* Protected routes */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRouteWithLayout>
              <DashboardPage />
            </ProtectedRouteWithLayout>
          }
        />
        <Route
          path="/playground"
          element={
            <ProtectedRouteWithLayout>
              <PlaygroundPage />
            </ProtectedRouteWithLayout>
          }
        />
        <Route
          path="/cta"
          element={
            <ProtectedRouteWithLayout>
              <CTAPage />
            </ProtectedRouteWithLayout>
          }
        />

        {/* Catch all route - redirect to login */}
        <Route path="*" element={<Navigate to="/auth/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
