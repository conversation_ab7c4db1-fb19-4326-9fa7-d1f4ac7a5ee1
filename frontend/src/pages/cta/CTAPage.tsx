/**
 * CTA (Call-to-Action) Page
 * Displays bookings and promotional content
 */
import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  CheckCircle,
  Star,
  ArrowRight,
  Sparkles,
  Target
} from 'lucide-react';

interface Booking {
  id: string;
  name: string;
  email: string;
  phone: string;
  service: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'completed';
}

const CTAPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'bookings' | 'promotions'>('bookings');

  const bookings: Booking[] = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      service: 'General Consultation',
      date: '2025-07-03',
      time: '10:00 AM',
      status: 'confirmed'
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567891',
      service: 'Technical Support',
      date: '2025-07-03',
      time: '2:00 PM',
      status: 'pending'
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567892',
      service: 'Course Enrollment',
      date: '2025-07-02',
      time: '11:00 AM',
      status: 'completed'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const promotions = [
    {
      id: 1,
      title: 'Premium AI Consultation',
      description: 'Get personalized AI-powered advice for your business needs',
      price: '$99',
      originalPrice: '$149',
      features: ['1-on-1 consultation', 'AI strategy planning', 'Implementation guide', '30-day support'],
      badge: 'Most Popular'
    },
    {
      id: 2,
      title: 'Technical Integration',
      description: 'Complete technical setup and integration support',
      price: '$199',
      originalPrice: '$299',
      features: ['Full system setup', 'API integration', 'Custom configuration', '60-day support'],
      badge: 'Best Value'
    },
    {
      id: 3,
      title: 'Enterprise Package',
      description: 'Comprehensive solution for large organizations',
      price: '$499',
      originalPrice: '$799',
      features: ['Unlimited consultations', 'Priority support', 'Custom development', '1-year warranty'],
      badge: 'Enterprise'
    }
  ];

  return (
    <div className="p-6 lg:p-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full">
            <Target className="h-8 w-8 text-white" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">CTA Dashboard</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Manage your bookings and explore our premium services designed to accelerate your success
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex justify-center">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('bookings')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
              activeTab === 'bookings'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Bookings
          </button>
          <button
            onClick={() => setActiveTab('promotions')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
              activeTab === 'promotions'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Promotions
          </button>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'bookings' ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Recent Bookings</h2>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              New Booking
            </button>
          </div>

          <div className="grid gap-4">
            {bookings.map((booking, index) => (
              <div
                key={booking.id}
                className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{booking.name}</h3>
                      <p className="text-sm text-gray-600">{booking.service}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                    {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>{booking.date}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{booking.time}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{booking.phone}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Premium Services</h2>
            <p className="text-gray-600">Choose the perfect plan for your needs</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {promotions.map((promo, index) => (
              <div
                key={promo.id}
                className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2 animate-fade-in-up relative"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {promo.badge && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                      <Sparkles className="h-3 w-3" />
                      <span>{promo.badge}</span>
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{promo.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{promo.description}</p>
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-3xl font-bold text-gray-900">{promo.price}</span>
                    <span className="text-lg text-gray-500 line-through">{promo.originalPrice}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  {promo.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 group">
                  <span>Get Started</span>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            ))}
          </div>

          {/* Testimonial Section */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 text-center">
            <div className="flex justify-center mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-lg text-gray-700 mb-4">
              "The AI consultation service transformed our business operations. Highly recommended!"
            </blockquote>
            <cite className="text-sm text-gray-600">- Sarah Johnson, CEO at TechCorp</cite>
          </div>
        </div>
      )}
    </div>
  );
};

export default CTAPage;
