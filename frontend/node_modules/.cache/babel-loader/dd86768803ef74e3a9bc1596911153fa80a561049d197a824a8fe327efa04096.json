{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport { PublicLayout } from './layouts';\nimport { authService } from './services';\n\n// Protected Route Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/auth/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Public Route Component (redirect to dashboard if already authenticated)\n_c = ProtectedRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(PublicLayout, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 10\n  }, this);\n};\n\n// Placeholder Dashboard Component\n_c2 = PublicRoute;\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-2\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Welcome to your dashboard!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-8 rounded-lg shadow-md space-y-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => authService.logout(),\n          className: \"w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_c3 = Dashboard;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/auth/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/login\",\n        element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n          children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/auth/signup\",\n        element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n          children: /*#__PURE__*/_jsxDEV(SignupPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/auth/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"Dashboard\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginPage", "SignupPage", "PublicLayout", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "isAuthenticated", "isAuthenticatedSync", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PublicRoute", "_c2", "Dashboard", "className", "onClick", "logout", "_c3", "App", "path", "element", "_c4", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport PlaygroundPage from './pages/playground/PlaygroundPage';\nimport CTAPage from './pages/cta/CTAPage';\nimport { PublicLayout, ProtectedLayout } from './layouts';\nimport { authService } from './services';\n\n// Protected Route Component\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Public Route Component (redirect to dashboard if already authenticated)\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <PublicLayout>{children}</PublicLayout>;\n};\n\n// Placeholder Dashboard Component\nconst Dashboard: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md w-full space-y-8 p-4\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Dashboard\n          </h1>\n          <p className=\"text-gray-600\">\n            Welcome to your dashboard!\n          </p>\n        </div>\n        <div className=\"bg-white p-8 rounded-lg shadow-md space-y-4\">\n          <button\n            onClick={() => authService.logout()}\n            className=\"w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700\"\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        {/* Default route - redirect to login */}\n        <Route path=\"/\" element={<Navigate to=\"/auth/login\" replace />} />\n\n        {/* Auth routes */}\n        <Route\n          path=\"/auth/login\"\n          element={\n            <PublicRoute>\n              <LoginPage />\n            </PublicRoute>\n          }\n        />\n        <Route\n          path=\"/auth/signup\"\n          element={\n            <PublicRoute>\n              <SignupPage />\n            </PublicRoute>\n          }\n        />\n\n        {/* Protected routes */}\n        <Route\n          path=\"/dashboard\"\n          element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          }\n        />\n\n        {/* Catch all route - redirect to login */}\n        <Route path=\"*\" element={<Navigate to=\"/auth/login\" replace />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,UAAU,MAAM,gCAAgC;AAIvD,SAASC,YAAY,QAAyB,WAAW;AACzD,SAASC,WAAW,QAAQ,YAAY;;AAExC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAChF,MAAMC,eAAe,GAAGP,WAAW,CAACQ,mBAAmB,CAAC,CAAC;EAEzD,IAAI,CAACD,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACN,QAAQ;MAACa,EAAE,EAAC,aAAa;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9C;EAEA,oBAAOZ,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAS,EAAA,GAVMV,cAAuD;AAW7D,MAAMW,WAAoD,GAAGA,CAAC;EAAEV;AAAS,CAAC,KAAK;EAC7E,MAAMC,eAAe,GAAGP,WAAW,CAACQ,mBAAmB,CAAC,CAAC;EAEzD,IAAID,eAAe,EAAE;IACnB,oBAAOL,OAAA,CAACN,QAAQ;MAACa,EAAE,EAAC,YAAY;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C;EAEA,oBAAOZ,OAAA,CAACH,YAAY;IAAAO,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAe,CAAC;AAChD,CAAC;;AAED;AAAAG,GAAA,GAVMD,WAAoD;AAW1D,MAAME,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,0DAA0D;IAAAb,QAAA,eACvEJ,OAAA;MAAKiB,SAAS,EAAC,+BAA+B;MAAAb,QAAA,gBAC5CJ,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAb,QAAA,gBAC1BJ,OAAA;UAAIiB,SAAS,EAAC,uCAAuC;UAAAb,QAAA,EAAC;QAEtD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLZ,OAAA;UAAGiB,SAAS,EAAC,eAAe;UAAAb,QAAA,EAAC;QAE7B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNZ,OAAA;QAAKiB,SAAS,EAAC,6CAA6C;QAAAb,QAAA,eAC1DJ,OAAA;UACEkB,OAAO,EAAEA,CAAA,KAAMpB,WAAW,CAACqB,MAAM,CAAC,CAAE;UACpCF,SAAS,EAAC,iEAAiE;UAAAb,QAAA,EAC5E;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,GAAA,GAvBIJ,SAAmB;AAyBzB,SAASK,GAAGA,CAAA,EAAG;EACb,oBACErB,OAAA,CAACT,MAAM;IAAAa,QAAA,eACLJ,OAAA,CAACR,MAAM;MAAAY,QAAA,gBAELJ,OAAA,CAACP,KAAK;QAAC6B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEvB,OAAA,CAACN,QAAQ;UAACa,EAAE,EAAC,aAAa;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlEZ,OAAA,CAACP,KAAK;QACJ6B,IAAI,EAAC,aAAa;QAClBC,OAAO,eACLvB,OAAA,CAACc,WAAW;UAAAV,QAAA,eACVJ,OAAA,CAACL,SAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACd;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFZ,OAAA,CAACP,KAAK;QACJ6B,IAAI,EAAC,cAAc;QACnBC,OAAO,eACLvB,OAAA,CAACc,WAAW;UAAAV,QAAA,eACVJ,OAAA,CAACJ,UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACd;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFZ,OAAA,CAACP,KAAK;QACJ6B,IAAI,EAAC,YAAY;QACjBC,OAAO,eACLvB,OAAA,CAACG,cAAc;UAAAC,QAAA,eACbJ,OAAA,CAACgB,SAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFZ,OAAA,CAACP,KAAK;QAAC6B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEvB,OAAA,CAACN,QAAQ;UAACa,EAAE,EAAC,aAAa;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACY,GAAA,GAxCQH,GAAG;AA0CZ,eAAeA,GAAG;AAAC,IAAAR,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}