{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/PublicLayout.tsx\";\n/**\n * Public Layout Component\n * Used for authentication pages (login, signup) - no sidebar\n */\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PublicLayout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-sm\",\n                children: \"CS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"Chat System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#features\",\n              className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#about\",\n              className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              className: \"text-gray-600 hover:text-gray-900 transition-colors\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"relative z-10 flex-1\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"relative z-10 bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 2025 Chat System. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm\",\n            children: \"Built with \\u2764\\uFE0F using React, TypeScript, and Tailwind CSS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = PublicLayout;\nexport default PublicLayout;\nvar _c;\n$RefreshReg$(_c, \"PublicLayout\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PublicLayout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/PublicLayout.tsx"], "sourcesContent": ["/**\n * Public Layout Component\n * Used for authentication pages (login, signup) - no sidebar\n */\nimport React from 'react';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nconst PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      \n      {/* Header */}\n      <header className=\"relative z-10 bg-white/80 backdrop-blur-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">CS</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Chat System</span>\n            </div>\n            \n            {/* Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#features\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                Features\n              </a>\n              <a href=\"#about\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                About\n              </a>\n              <a href=\"#contact\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\n                Contact\n              </a>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"relative z-10 flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"relative z-10 bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-auto\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>&copy; 2025 Chat System. All rights reserved.</p>\n            <p className=\"mt-2 text-sm\">\n              Built with ❤️ using React, TypeScript, and Tailwind CSS\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default PublicLayout;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM1B,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAClE,oBACEF,OAAA;IAAKG,SAAS,EAAC,oEAAoE;IAAAD,QAAA,gBAEjFF,OAAA;MAAKG,SAAS,EAAC;IAA4C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGlEP,OAAA;MAAQG,SAAS,EAAC,qEAAqE;MAAAD,QAAA,eACrFF,OAAA;QAAKG,SAAS,EAAC,wCAAwC;QAAAD,QAAA,eACrDF,OAAA;UAAKG,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBAErDF,OAAA;YAAKG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CF,OAAA;cAAKG,SAAS,EAAC,kGAAkG;cAAAD,QAAA,eAC/GF,OAAA;gBAAMG,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNP,OAAA;cAAMG,SAAS,EAAC,iCAAiC;cAAAD,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAGNP,OAAA;YAAKG,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDF,OAAA;cAAGQ,IAAI,EAAC,WAAW;cAACL,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAAC;YAEpF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,QAAQ;cAACL,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,UAAU;cAACL,SAAS,EAAC,qDAAqD;cAAAD,QAAA,EAAC;YAEnF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTP,OAAA;MAAMG,SAAS,EAAC,sBAAsB;MAAAD,QAAA,EACnCA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPP,OAAA;MAAQG,SAAS,EAAC,6EAA6E;MAAAD,QAAA,eAC7FF,OAAA;QAAKG,SAAS,EAAC,6CAA6C;QAAAD,QAAA,eAC1DF,OAAA;UAAKG,SAAS,EAAC,2BAA2B;UAAAD,QAAA,gBACxCF,OAAA;YAAAE,QAAA,EAAG;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpDP,OAAA;YAAGG,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GApDIR,YAAyC;AAsD/C,eAAeA,YAAY;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}