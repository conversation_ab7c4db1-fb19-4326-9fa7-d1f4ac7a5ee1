{"ast": null, "code": "import _objectSpread from\"/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * Login Form Component\n * Presentational component for user login\n */import React,{useState}from'react';import{Mail,Lock,LogIn}from'lucide-react';import{Button,Input}from'../../../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginForm=_ref=>{let{onSubmit,isLoading=false,error}=_ref;const[formData,setFormData]=useState({email:'',password:''});const[formErrors,setFormErrors]=useState({});const validateForm=()=>{const errors={};if(!formData.email){errors.email='Email is required';}else if(!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)){errors.email='Please enter a valid email address';}if(!formData.password){errors.password='Password is required';}else if(formData.password.length<6){errors.password='Password must be at least 6 characters';}setFormErrors(errors);return Object.keys(errors).length===0;};const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{await onSubmit(formData);}catch(error){// Error handling is done by parent component\n}};const handleInputChange=field=>e=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:e.target.value}));// Clear field error when user starts typing\nif(formErrors[field]){setFormErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:undefined}));}};return/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-gray-900\",children:\"Welcome Back\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Sign in to your account\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-lg p-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-red-600 text-sm\",children:error})}),/*#__PURE__*/_jsx(Input,{label:\"Email Address\",type:\"email\",placeholder:\"Enter your email\",value:formData.email,onChange:handleInputChange('email'),error:formErrors.email,leftIcon:/*#__PURE__*/_jsx(Mail,{className:\"h-4 w-4\"}),disabled:isLoading,required:true}),/*#__PURE__*/_jsx(Input,{label:\"Password\",type:\"password\",placeholder:\"Enter your password\",value:formData.password,onChange:handleInputChange('password'),error:formErrors.password,leftIcon:/*#__PURE__*/_jsx(Lock,{className:\"h-4 w-4\"}),disabled:isLoading,required:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"remember-me\",name:\"remember-me\",type:\"checkbox\",className:\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"remember-me\",className:\"ml-2 block text-sm text-gray-700\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"font-medium text-primary-600 hover:text-primary-500\",children:\"Forgot your password?\"})})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",size:\"lg\",fullWidth:true,isLoading:isLoading,leftIcon:!isLoading?/*#__PURE__*/_jsx(LogIn,{className:\"h-4 w-4\"}):undefined,children:isLoading?'Signing in...':'Sign In'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 text-center\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[\"Don't have an account?\",' ',/*#__PURE__*/_jsx(\"a\",{href:\"/auth/signup\",className:\"font-medium text-primary-600 hover:text-primary-500\",children:\"Sign up here\"})]})})]});};export default LoginForm;", "map": {"version": 3, "names": ["React", "useState", "Mail", "Lock", "LogIn", "<PERSON><PERSON>", "Input", "jsx", "_jsx", "jsxs", "_jsxs", "LoginForm", "_ref", "onSubmit", "isLoading", "error", "formData", "setFormData", "email", "password", "formErrors", "setFormErrors", "validateForm", "errors", "test", "length", "Object", "keys", "handleSubmit", "e", "preventDefault", "handleInputChange", "field", "prev", "_objectSpread", "target", "value", "undefined", "className", "children", "label", "type", "placeholder", "onChange", "leftIcon", "disabled", "required", "id", "name", "htmlFor", "href", "variant", "size", "fullWidth"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/components/auth.loginForm.tsx"], "sourcesContent": ["/**\n * Login Form Component\n * Presentational component for user login\n */\nimport React, { useState } from 'react';\nimport { Mail, Lock, LogIn } from 'lucide-react';\nimport { Button, Input } from '../../../components';\nimport { LoginRequest } from '../../../types';\n\ninterface LoginFormProps {\n  onSubmit: (credentials: LoginRequest) => Promise<void>;\n  isLoading?: boolean;\n  error?: string;\n}\n\nconst LoginForm: React.FC<LoginFormProps> = ({\n  onSubmit,\n  isLoading = false,\n  error\n}) => {\n  const [formData, setFormData] = useState<LoginRequest>({\n    email: '',\n    password: ''\n  });\n  \n  const [formErrors, setFormErrors] = useState<Partial<LoginRequest>>({});\n\n  const validateForm = (): boolean => {\n    const errors: Partial<LoginRequest> = {};\n    \n    if (!formData.email) {\n      errors.email = 'Email is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    \n    if (!formData.password) {\n      errors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      errors.password = 'Password must be at least 6 characters';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n    \n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      // Error handling is done by parent component\n    }\n  };\n\n  const handleInputChange = (field: keyof LoginRequest) => (\n    e: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: e.target.value\n    }));\n    \n    // Clear field error when user starts typing\n    if (formErrors[field]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [field]: undefined\n      }));\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-gray-900\">Welcome Back</h2>\n        <p className=\"mt-2 text-gray-600\">Sign in to your account</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        )}\n\n        <Input\n          label=\"Email Address\"\n          type=\"email\"\n          placeholder=\"Enter your email\"\n          value={formData.email}\n          onChange={handleInputChange('email')}\n          error={formErrors.email}\n          leftIcon={<Mail className=\"h-4 w-4\" />}\n          disabled={isLoading}\n          required\n        />\n\n        <Input\n          label=\"Password\"\n          type=\"password\"\n          placeholder=\"Enter your password\"\n          value={formData.password}\n          onChange={handleInputChange('password')}\n          error={formErrors.password}\n          leftIcon={<Lock className=\"h-4 w-4\" />}\n          disabled={isLoading}\n          required\n        />\n\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <input\n              id=\"remember-me\"\n              name=\"remember-me\"\n              type=\"checkbox\"\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-700\">\n              Remember me\n            </label>\n          </div>\n\n          <div className=\"text-sm\">\n            <a\n              href=\"#\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              Forgot your password?\n            </a>\n          </div>\n        </div>\n\n        <Button\n          type=\"submit\"\n          variant=\"primary\"\n          size=\"lg\"\n          fullWidth\n          isLoading={isLoading}\n          leftIcon={!isLoading ? <LogIn className=\"h-4 w-4\" /> : undefined}\n        >\n          {isLoading ? 'Signing in...' : 'Sign In'}\n        </Button>\n      </form>\n\n      <div className=\"mt-6 text-center\">\n        <p className=\"text-sm text-gray-600\">\n          Don't have an account?{' '}\n          <a\n            href=\"/auth/signup\"\n            className=\"font-medium text-primary-600 hover:text-primary-500\"\n          >\n            Sign up here\n          </a>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginForm;\n"], "mappings": "wIAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,IAAI,CAAEC,KAAK,KAAQ,cAAc,CAChD,OAASC,MAAM,CAAEC,KAAK,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASpD,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAItC,IAJuC,CAC3CC,QAAQ,CACRC,SAAS,CAAG,KAAK,CACjBC,KACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAe,CACrDiB,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGpB,QAAQ,CAAwB,CAAC,CAAC,CAAC,CAEvE,KAAM,CAAAqB,YAAY,CAAGA,CAAA,GAAe,CAClC,KAAM,CAAAC,MAA6B,CAAG,CAAC,CAAC,CAExC,GAAI,CAACP,QAAQ,CAACE,KAAK,CAAE,CACnBK,MAAM,CAACL,KAAK,CAAG,mBAAmB,CACpC,CAAC,IAAM,IAAI,CAAC,4BAA4B,CAACM,IAAI,CAACR,QAAQ,CAACE,KAAK,CAAC,CAAE,CAC7DK,MAAM,CAACL,KAAK,CAAG,oCAAoC,CACrD,CAEA,GAAI,CAACF,QAAQ,CAACG,QAAQ,CAAE,CACtBI,MAAM,CAACJ,QAAQ,CAAG,sBAAsB,CAC1C,CAAC,IAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACM,MAAM,CAAG,CAAC,CAAE,CACvCF,MAAM,CAACJ,QAAQ,CAAG,wCAAwC,CAC5D,CAEAE,aAAa,CAACE,MAAM,CAAC,CACrB,MAAO,CAAAG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACE,MAAM,GAAK,CAAC,CACzC,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACR,YAAY,CAAC,CAAC,CAAE,CACnB,OACF,CAEA,GAAI,CACF,KAAM,CAAAT,QAAQ,CAACG,QAAQ,CAAC,CAC1B,CAAE,MAAOD,KAAK,CAAE,CACd;AAAA,CAEJ,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAIC,KAAyB,EAClDH,CAAsC,EACnC,CACHZ,WAAW,CAACgB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACD,KAAK,EAAGH,CAAC,CAACM,MAAM,CAACC,KAAK,EACvB,CAAC,CAEH;AACA,GAAIhB,UAAU,CAACY,KAAK,CAAC,CAAE,CACrBX,aAAa,CAACY,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACbD,IAAI,MACP,CAACD,KAAK,EAAGK,SAAS,EAClB,CAAC,CACL,CACF,CAAC,CAED,mBACE3B,KAAA,QAAK4B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC7B,KAAA,QAAK4B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B/B,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAClE/B,IAAA,MAAG8B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,yBAAuB,CAAG,CAAC,EAC1D,CAAC,cAEN7B,KAAA,SAAMG,QAAQ,CAAEe,YAAa,CAACU,SAAS,CAAC,WAAW,CAAAC,QAAA,EAChDxB,KAAK,eACJP,IAAA,QAAK8B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cAC7D/B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAExB,KAAK,CAAI,CAAC,CAC5C,CACN,cAEDP,IAAA,CAACF,KAAK,EACJkC,KAAK,CAAC,eAAe,CACrBC,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,kBAAkB,CAC9BN,KAAK,CAAEpB,QAAQ,CAACE,KAAM,CACtByB,QAAQ,CAAEZ,iBAAiB,CAAC,OAAO,CAAE,CACrChB,KAAK,CAAEK,UAAU,CAACF,KAAM,CACxB0B,QAAQ,cAAEpC,IAAA,CAACN,IAAI,EAACoC,SAAS,CAAC,SAAS,CAAE,CAAE,CACvCO,QAAQ,CAAE/B,SAAU,CACpBgC,QAAQ,MACT,CAAC,cAEFtC,IAAA,CAACF,KAAK,EACJkC,KAAK,CAAC,UAAU,CAChBC,IAAI,CAAC,UAAU,CACfC,WAAW,CAAC,qBAAqB,CACjCN,KAAK,CAAEpB,QAAQ,CAACG,QAAS,CACzBwB,QAAQ,CAAEZ,iBAAiB,CAAC,UAAU,CAAE,CACxChB,KAAK,CAAEK,UAAU,CAACD,QAAS,CAC3ByB,QAAQ,cAAEpC,IAAA,CAACL,IAAI,EAACmC,SAAS,CAAC,SAAS,CAAE,CAAE,CACvCO,QAAQ,CAAE/B,SAAU,CACpBgC,QAAQ,MACT,CAAC,cAEFpC,KAAA,QAAK4B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,UACEuC,EAAE,CAAC,aAAa,CAChBC,IAAI,CAAC,aAAa,CAClBP,IAAI,CAAC,UAAU,CACfH,SAAS,CAAC,yEAAyE,CACpF,CAAC,cACF9B,IAAA,UAAOyC,OAAO,CAAC,aAAa,CAACX,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,aAE1E,CAAO,CAAC,EACL,CAAC,cAEN/B,IAAA,QAAK8B,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB/B,IAAA,MACE0C,IAAI,CAAC,GAAG,CACRZ,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAChE,uBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,cAEN/B,IAAA,CAACH,MAAM,EACLoC,IAAI,CAAC,QAAQ,CACbU,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTC,SAAS,MACTvC,SAAS,CAAEA,SAAU,CACrB8B,QAAQ,CAAE,CAAC9B,SAAS,cAAGN,IAAA,CAACJ,KAAK,EAACkC,SAAS,CAAC,SAAS,CAAE,CAAC,CAAGD,SAAU,CAAAE,QAAA,CAEhEzB,SAAS,CAAG,eAAe,CAAG,SAAS,CAClC,CAAC,EACL,CAAC,cAEPN,IAAA,QAAK8B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B7B,KAAA,MAAG4B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,wBACb,CAAC,GAAG,cAC1B/B,IAAA,MACE0C,IAAI,CAAC,cAAc,CACnBZ,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAChE,cAED,CAAG,CAAC,EACH,CAAC,CACD,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}