{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M16 12h.01\",\n  key: \"1l6xoz\"\n}], [\"path\", {\n  d: \"m17 7 5 5-5 5\",\n  key: \"1xlxn0\"\n}], [\"path\", {\n  d: \"m7 7-5 5 5 5\",\n  key: \"19njba\"\n}], [\"path\", {\n  d: \"M8 12h.01\",\n  key: \"czm47f\"\n}]];\nconst ChevronsLeftRightEllipsis = createLucideIcon(\"chevrons-left-right-ellipsis\", __iconNode);\nexport { __iconNode, ChevronsLeftRightEllipsis as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ChevronsLeftRightEllipsis", "createLucideIcon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/lucide-react/src/icons/chevrons-left-right-ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 12h.01', key: '1mp3jc' }],\n  ['path', { d: 'M16 12h.01', key: '1l6xoz' }],\n  ['path', { d: 'm17 7 5 5-5 5', key: '1xlxn0' }],\n  ['path', { d: 'm7 7-5 5 5 5', key: '19njba' }],\n  ['path', { d: 'M8 12h.01', key: 'czm47f' }],\n];\n\n/**\n * @component @name ChevronsLeftRightEllipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTJoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMmguMDEiIC8+CiAgPHBhdGggZD0ibTE3IDcgNSA1LTUgNSIgLz4KICA8cGF0aCBkPSJtNyA3LTUgNSA1IDUiIC8+CiAgPHBhdGggZD0iTTggMTJoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevrons-left-right-ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronsLeftRightEllipsis = createLucideIcon('chevrons-left-right-ellipsis', __iconNode);\n\nexport default ChevronsLeftRightEllipsis;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAC,yBAAA,GAA4BC,gBAAiB,iCAAgCJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}