#!/usr/bin/env python3
"""
Database Setup and Verification Script
This script will:
1. Test MongoDB connection
2. Create database and collections
3. Set up indexes
4. Create sample data
5. Verify everything is working
"""

import asyncio
import sys
import os
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from config.database import (
    db_config, 
    get_db, 
    get_collection, 
    COLLECTIONS,
    initialize_collections,
    create_sample_time_slots
)
from pymongo.errors import ConnectionFailure
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_connection():
    """Test MongoDB connection"""
    print("🔍 Testing MongoDB connection...")
    try:
        success = await db_config.connect()
        if success:
            print("✅ MongoDB connection successful!")
            return True
        else:
            print("❌ MongoDB connection failed!")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def create_database_and_collections():
    """Create database and collections"""
    print("\n📦 Creating database and collections...")
    try:
        db = await get_db()
        
        # Create collections if they don't exist
        existing_collections = await db.list_collection_names()
        
        for collection_name in COLLECTIONS.values():
            if collection_name not in existing_collections:
                await db.create_collection(collection_name)
                print(f"✅ Created collection: {collection_name}")
            else:
                print(f"ℹ️  Collection already exists: {collection_name}")
        
        return True
    except Exception as e:
        print(f"❌ Error creating collections: {e}")
        return False

async def setup_indexes():
    """Set up database indexes"""
    print("\n🔧 Setting up database indexes...")
    try:
        await initialize_collections()
        print("✅ Database indexes created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating indexes: {e}")
        return False

async def create_sample_data():
    """Create sample data for testing"""
    print("\n📝 Creating sample data...")
    try:
        # Create sample time slots
        await create_sample_time_slots()
        
        # Create a sample user
        users_collection = await get_collection(COLLECTIONS['users'])
        sample_user = {
            "name": "Demo User",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "password_hash": "$argon2id$v=19$m=65536,t=3,p=4$example_hash",  # demo123
            "is_active": True,
            "created_at": datetime.utcnow(),
            "preferences": {}
        }
        
        # Check if user already exists
        existing_user = await users_collection.find_one({"email": sample_user["email"]})
        if not existing_user:
            await users_collection.insert_one(sample_user)
            print("✅ Created sample user (<EMAIL> / demo123)")
        else:
            print("ℹ️  Sample user already exists")
        
        return True
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

async def verify_database():
    """Verify database setup"""
    print("\n🔍 Verifying database setup...")
    try:
        db = await get_db()
        
        # Check collections
        collections = await db.list_collection_names()
        print(f"📋 Collections found: {collections}")
        
        # Check data counts
        for collection_name in COLLECTIONS.values():
            collection = await get_collection(collection_name)
            count = await collection.count_documents({})
            print(f"📊 {collection_name}: {count} documents")
        
        # Test a simple query
        users_collection = await get_collection(COLLECTIONS['users'])
        user_count = await users_collection.count_documents({})
        print(f"👥 Total users: {user_count}")
        
        time_slots_collection = await get_collection(COLLECTIONS['time_slots'])
        slots_count = await time_slots_collection.count_documents({})
        print(f"⏰ Total time slots: {slots_count}")
        
        print("✅ Database verification completed!")
        return True
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        return False

async def show_database_info():
    """Show detailed database information"""
    print("\n📊 Database Information:")
    try:
        db = await get_db()
        
        # Database stats
        stats = await db.command("dbStats")
        print(f"Database: {stats['db']}")
        print(f"Collections: {stats['collections']}")
        print(f"Data Size: {stats['dataSize']} bytes")
        print(f"Storage Size: {stats['storageSize']} bytes")
        
        # Show sample documents
        print("\n📄 Sample Documents:")
        
        # Sample user
        users_collection = await get_collection(COLLECTIONS['users'])
        sample_user = await users_collection.find_one({})
        if sample_user:
            print(f"Sample User: {sample_user.get('name')} ({sample_user.get('email')})")
        
        # Sample time slot
        time_slots_collection = await get_collection(COLLECTIONS['time_slots'])
        sample_slot = await time_slots_collection.find_one({})
        if sample_slot:
            print(f"Sample Time Slot: {sample_slot.get('date')} {sample_slot.get('time')}")
        
        return True
    except Exception as e:
        print(f"❌ Error getting database info: {e}")
        return False

async def main():
    """Main setup function"""
    print("🚀 Starting Database Setup and Verification")
    print("=" * 50)
    
    # Step 1: Test connection
    if not await test_connection():
        print("\n❌ Cannot proceed without database connection!")
        return False
    
    # Step 2: Create database and collections
    if not await create_database_and_collections():
        print("\n❌ Failed to create database structure!")
        return False
    
    # Step 3: Set up indexes
    if not await setup_indexes():
        print("\n❌ Failed to set up indexes!")
        return False
    
    # Step 4: Create sample data
    if not await create_sample_data():
        print("\n❌ Failed to create sample data!")
        return False
    
    # Step 5: Verify everything
    if not await verify_database():
        print("\n❌ Database verification failed!")
        return False
    
    # Step 6: Show database info
    await show_database_info()
    
    print("\n" + "=" * 50)
    print("🎉 Database setup completed successfully!")
    print("\nYou can now:")
    print("1. Start the backend server: cd backend && python main.py")
    print("2. Test the API endpoints")
    print("3. Use the frontend application")
    print("\nDemo credentials:")
    print("Email: <EMAIL>")
    print("Password: demo123")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
