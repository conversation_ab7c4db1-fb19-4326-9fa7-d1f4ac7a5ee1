{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 19h6\",\n  key: \"456am0\"\n}], [\"path\", {\n  d: \"M9 15v-3H5l7-7 7 7h-4v3H9z\",\n  key: \"1r2uve\"\n}]];\nconst ArrowBigUpDash = createLucideIcon(\"arrow-big-up-dash\", __iconNode);\nexport { __iconNode, ArrowBigUpDash as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ArrowBigUpDash", "createLucideIcon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/lucide-react/src/icons/arrow-big-up-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M9 19h6', key: '456am0' }],\n  ['path', { d: 'M9 15v-3H5l7-7 7 7h-4v3H9z', key: '1r2uve' }],\n];\n\n/**\n * @component @name ArrowBigUpDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAxOWg2IiAvPgogIDxwYXRoIGQ9Ik05IDE1di0zSDVsNy03IDcgN2gtNHYzSDl6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-big-up-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigUpDash = createLucideIcon('arrow-big-up-dash', __iconNode);\n\nexport default ArrowBigUpDash;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAU,GAC7D;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}