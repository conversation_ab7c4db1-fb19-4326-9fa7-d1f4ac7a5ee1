{"ast": null, "code": "import React from'react';import{Button,Input}from'./components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full space-y-8 p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold text-gray-900 mb-2\",children:\"Chat System\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Advanced chat system with booking and analytics\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-8 rounded-lg shadow-md space-y-4\",children:[/*#__PURE__*/_jsx(Input,{label:\"Test Input\",placeholder:\"Enter something...\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",fullWidth:true,children:\"Test Button\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83D\\uDE80 Backend: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-mono\",children:\"http://localhost:8000\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83D\\uDCF1 Frontend: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-mono\",children:\"http://localhost:3000\"})]})]})]})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Input", "jsx", "_jsx", "jsxs", "_jsxs", "App", "className", "children", "label", "placeholder", "variant", "fullWidth"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Button, Input } from './components';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md w-full space-y-8 p-4\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Chat System\n          </h1>\n          <p className=\"text-gray-600\">\n            Advanced chat system with booking and analytics\n          </p>\n        </div>\n\n        <div className=\"bg-white p-8 rounded-lg shadow-md space-y-4\">\n          <Input\n            label=\"Test Input\"\n            placeholder=\"Enter something...\"\n          />\n\n          <Button variant=\"primary\" fullWidth>\n            Test Button\n          </Button>\n\n          <div className=\"text-center text-sm text-gray-500\">\n            <p>🚀 Backend: <span className=\"font-mono\">http://localhost:8000</span></p>\n            <p>📱 Frontend: <span className=\"font-mono\">http://localhost:3000</span></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAKI,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvEH,KAAA,QAAKE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,OAAII,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,aAEtD,CAAI,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAE7B,CAAG,CAAC,EACD,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DL,IAAA,CAACF,KAAK,EACJQ,KAAK,CAAC,YAAY,CAClBC,WAAW,CAAC,oBAAoB,CACjC,CAAC,cAEFP,IAAA,CAACH,MAAM,EAACW,OAAO,CAAC,SAAS,CAACC,SAAS,MAAAJ,QAAA,CAAC,aAEpC,CAAQ,CAAC,cAETH,KAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDH,KAAA,MAAAG,QAAA,EAAG,wBAAY,cAAAL,IAAA,SAAMI,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,uBAAqB,CAAM,CAAC,EAAG,CAAC,cAC3EH,KAAA,MAAAG,QAAA,EAAG,yBAAa,cAAAL,IAAA,SAAMI,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,uBAAqB,CAAM,CAAC,EAAG,CAAC,EACzE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA,cAAe,CAAAF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}