{"ast": null, "code": "/**\n * Authentication service\n * Handles user authentication, registration, and session management\n */\nimport baseHttp from './baseHttp';\nclass AuthService {\n  constructor() {\n    this.AUTH_ENDPOINTS = {\n      LOGIN: '/api/auth/login',\n      REGISTER: '/api/auth/register',\n      LOGOUT: '/api/auth/logout',\n      ME: '/api/auth/me',\n      CHECK: '/api/auth/check',\n      UPDATE_PROFILE: '/api/auth/me'\n    };\n  }\n  /**\n   * Login user with email and password\n   */\n  async login(credentials) {\n    try {\n      const response = await baseHttp.post(this.AUTH_ENDPOINTS.LOGIN, credentials, {\n        withCredentials: true\n      } // Include cookies\n      );\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n      return response;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Register new user\n   */\n  async register(userData) {\n    try {\n      const response = await baseHttp.post(this.AUTH_ENDPOINTS.REGISTER, userData, {\n        withCredentials: true\n      } // Include cookies\n      );\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n      return response;\n    } catch (error) {\n      console.error('Registration failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current user information\n   */\n  async getCurrentUser() {\n    try {\n      return await baseHttp.get(this.AUTH_ENDPOINTS.ME, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Failed to get current user:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update user profile\n   */\n  async updateProfile(userData) {\n    try {\n      const response = await baseHttp.put(this.AUTH_ENDPOINTS.UPDATE_PROFILE, userData);\n\n      // Update stored user data\n      this.updateStoredUserData(response);\n      return response;\n    } catch (error) {\n      console.error('Profile update failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout() {\n    try {\n      await baseHttp.post(this.AUTH_ENDPOINTS.LOGOUT, {}, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      this.clearAuthData();\n      // Redirect to login page\n      window.location.href = '/auth/login';\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  async isAuthenticated() {\n    try {\n      const response = await baseHttp.get(this.AUTH_ENDPOINTS.CHECK, {\n        withCredentials: true\n      });\n      return response.authenticated;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Check if user is authenticated (sync version)\n   */\n  isAuthenticatedSync() {\n    const user = this.getStoredUser();\n    return !!user;\n  }\n\n  /**\n   * Get stored user data\n   */\n  getStoredUser() {\n    const userData = localStorage.getItem('user_data');\n    if (userData) {\n      try {\n        return JSON.parse(userData);\n      } catch (error) {\n        console.error('Failed to parse stored user data:', error);\n        this.clearAuthData();\n        return null;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Store user data locally\n   */\n  storeUserData(user) {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Update stored user data\n   */\n  updateStoredUserData(user) {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Clear all authentication data\n   */\n  clearAuthData() {\n    localStorage.removeItem('user_data');\n  }\n\n  /**\n   * Initialize auth service (call on app startup)\n   */\n  initialize() {\n    // No token initialization needed for session-based auth\n    // Session is handled by cookies automatically\n  }\n\n  /**\n   * Validate session and refresh user data\n   */\n  async validateAndRefreshAuth() {\n    try {\n      const isAuth = await this.isAuthenticated();\n      if (!isAuth) {\n        this.clearAuthData();\n        return false;\n      }\n      const user = await this.getCurrentUser();\n      this.updateStoredUserData(user);\n      return true;\n    } catch (error) {\n      console.error('Auth validation failed:', error);\n      this.clearAuthData();\n      return false;\n    }\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;\n\n// Export the class for testing purposes\nexport { AuthService };", "map": {"version": 3, "names": ["baseHttp", "AuthService", "constructor", "AUTH_ENDPOINTS", "LOGIN", "REGISTER", "LOGOUT", "ME", "CHECK", "UPDATE_PROFILE", "login", "credentials", "response", "post", "withCredentials", "success", "user", "storeUserData", "error", "console", "register", "userData", "getCurrentUser", "get", "updateProfile", "put", "updateStoredUserData", "logout", "clearAuthData", "window", "location", "href", "isAuthenticated", "authenticated", "isAuthenticatedSync", "getStoredUser", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "removeItem", "initialize", "validateAndRefreshAuth", "isAuth", "authService"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts"], "sourcesContent": ["/**\n * Authentication service\n * Handles user authentication, registration, and session management\n */\nimport baseHttp from './baseHttp';\nimport { User, LoginRequest, RegisterRequest } from '../types';\n\ninterface LoginResponse {\n  success: boolean;\n  message: string;\n  user?: User;\n}\n\nclass AuthService {\n  private readonly AUTH_ENDPOINTS = {\n    LOGIN: '/api/auth/login',\n    REGISTER: '/api/auth/register',\n    LOGOUT: '/api/auth/logout',\n    ME: '/api/auth/me',\n    CHECK: '/api/auth/check',\n    UPDATE_PROFILE: '/api/auth/me',\n  };\n\n  /**\n   * Login user with email and password\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await baseHttp.post<LoginResponse>(\n        this.AUTH_ENDPOINTS.LOGIN,\n        credentials,\n        { withCredentials: true } // Include cookies\n      );\n\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n\n      return response;\n    } catch (error) {\n      console.error('Lo<PERSON> failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Register new user\n   */\n  async register(userData: RegisterRequest): Promise<LoginResponse> {\n    try {\n      const response = await baseHttp.post<LoginResponse>(\n        this.AUTH_ENDPOINTS.REGISTER,\n        userData,\n        { withCredentials: true } // Include cookies\n      );\n\n      if (response.success && response.user) {\n        // Store user data locally\n        this.storeUserData(response.user);\n      }\n\n      return response;\n    } catch (error) {\n      console.error('Registration failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current user information\n   */\n  async getCurrentUser(): Promise<User> {\n    try {\n      return await baseHttp.get<User>(this.AUTH_ENDPOINTS.ME, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Failed to get current user:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update user profile\n   */\n  async updateProfile(userData: Partial<User>): Promise<User> {\n    try {\n      const response = await baseHttp.put<User>(\n        this.AUTH_ENDPOINTS.UPDATE_PROFILE,\n        userData\n      );\n\n      // Update stored user data\n      this.updateStoredUserData(response);\n\n      return response;\n    } catch (error) {\n      console.error('Profile update failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await baseHttp.post(this.AUTH_ENDPOINTS.LOGOUT, {}, {\n        withCredentials: true\n      });\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      this.clearAuthData();\n      // Redirect to login page\n      window.location.href = '/auth/login';\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  async isAuthenticated(): Promise<boolean> {\n    try {\n      const response = await baseHttp.get(this.AUTH_ENDPOINTS.CHECK, {\n        withCredentials: true\n      });\n      return response.authenticated;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Check if user is authenticated (sync version)\n   */\n  isAuthenticatedSync(): boolean {\n    const user = this.getStoredUser();\n    return !!user;\n  }\n\n  /**\n   * Get stored user data\n   */\n  getStoredUser(): User | null {\n    const userData = localStorage.getItem('user_data');\n    if (userData) {\n      try {\n        return JSON.parse(userData);\n      } catch (error) {\n        console.error('Failed to parse stored user data:', error);\n        this.clearAuthData();\n        return null;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Store user data locally\n   */\n  private storeUserData(user: User): void {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Update stored user data\n   */\n  private updateStoredUserData(user: User): void {\n    localStorage.setItem('user_data', JSON.stringify(user));\n  }\n\n  /**\n   * Clear all authentication data\n   */\n  private clearAuthData(): void {\n    localStorage.removeItem('user_data');\n  }\n\n  /**\n   * Initialize auth service (call on app startup)\n   */\n  initialize(): void {\n    // No token initialization needed for session-based auth\n    // Session is handled by cookies automatically\n  }\n\n  /**\n   * Validate session and refresh user data\n   */\n  async validateAndRefreshAuth(): Promise<boolean> {\n    try {\n      const isAuth = await this.isAuthenticated();\n      if (!isAuth) {\n        this.clearAuthData();\n        return false;\n      }\n\n      const user = await this.getCurrentUser();\n      this.updateStoredUserData(user);\n      return true;\n    } catch (error) {\n      console.error('Auth validation failed:', error);\n      this.clearAuthData();\n      return false;\n    }\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;\n\n// Export the class for testing purposes\nexport { AuthService };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,YAAY;AASjC,MAAMC,WAAW,CAAC;EAAAC,YAAA;IAAA,KACCC,cAAc,GAAG;MAChCC,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAE,oBAAoB;MAC9BC,MAAM,EAAE,kBAAkB;MAC1BC,EAAE,EAAE,cAAc;MAClBC,KAAK,EAAE,iBAAiB;MACxBC,cAAc,EAAE;IAClB,CAAC;EAAA;EAED;AACF;AACA;EACE,MAAMC,KAAKA,CAACC,WAAyB,EAA0B;IAC7D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAClC,IAAI,CAACV,cAAc,CAACC,KAAK,EACzBO,WAAW,EACX;QAAEG,eAAe,EAAE;MAAK,CAAC,CAAC;MAC5B,CAAC;MAED,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrC;QACA,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACI,IAAI,CAAC;MACnC;MAEA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,QAAQA,CAACC,QAAyB,EAA0B;IAChE,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAClC,IAAI,CAACV,cAAc,CAACE,QAAQ,EAC5BgB,QAAQ,EACR;QAAEP,eAAe,EAAE;MAAK,CAAC,CAAC;MAC5B,CAAC;MAED,IAAIF,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrC;QACA,IAAI,CAACC,aAAa,CAACL,QAAQ,CAACI,IAAI,CAAC;MACnC;MAEA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMI,cAAcA,CAAA,EAAkB;IACpC,IAAI;MACF,OAAO,MAAMtB,QAAQ,CAACuB,GAAG,CAAO,IAAI,CAACpB,cAAc,CAACI,EAAE,EAAE;QACtDO,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMM,aAAaA,CAACH,QAAuB,EAAiB;IAC1D,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMZ,QAAQ,CAACyB,GAAG,CACjC,IAAI,CAACtB,cAAc,CAACM,cAAc,EAClCY,QACF,CAAC;;MAED;MACA,IAAI,CAACK,oBAAoB,CAACd,QAAQ,CAAC;MAEnC,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMS,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAM3B,QAAQ,CAACa,IAAI,CAAC,IAAI,CAACV,cAAc,CAACG,MAAM,EAAE,CAAC,CAAC,EAAE;QAClDQ,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACR,IAAI,CAACU,aAAa,CAAC,CAAC;MACpB;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,aAAa;IACtC;EACF;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAAA,EAAqB;IACxC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMZ,QAAQ,CAACuB,GAAG,CAAC,IAAI,CAACpB,cAAc,CAACK,KAAK,EAAE;QAC7DM,eAAe,EAAE;MACnB,CAAC,CAAC;MACF,OAAOF,QAAQ,CAACqB,aAAa;IAC/B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACEgB,mBAAmBA,CAAA,EAAY;IAC7B,MAAMlB,IAAI,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC;IACjC,OAAO,CAAC,CAACnB,IAAI;EACf;;EAEA;AACF;AACA;EACEmB,aAAaA,CAAA,EAAgB;IAC3B,MAAMd,QAAQ,GAAGe,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAClD,IAAIhB,QAAQ,EAAE;MACZ,IAAI;QACF,OAAOiB,IAAI,CAACC,KAAK,CAAClB,QAAQ,CAAC;MAC7B,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACU,aAAa,CAAC,CAAC;QACpB,OAAO,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACUX,aAAaA,CAACD,IAAU,EAAQ;IACtCoB,YAAY,CAACI,OAAO,CAAC,WAAW,EAAEF,IAAI,CAACG,SAAS,CAACzB,IAAI,CAAC,CAAC;EACzD;;EAEA;AACF;AACA;EACUU,oBAAoBA,CAACV,IAAU,EAAQ;IAC7CoB,YAAY,CAACI,OAAO,CAAC,WAAW,EAAEF,IAAI,CAACG,SAAS,CAACzB,IAAI,CAAC,CAAC;EACzD;;EAEA;AACF;AACA;EACUY,aAAaA,CAAA,EAAS;IAC5BQ,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC;EACtC;;EAEA;AACF;AACA;EACEC,UAAUA,CAAA,EAAS;IACjB;IACA;EAAA;;EAGF;AACF;AACA;EACE,MAAMC,sBAAsBA,CAAA,EAAqB;IAC/C,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACb,eAAe,CAAC,CAAC;MAC3C,IAAI,CAACa,MAAM,EAAE;QACX,IAAI,CAACjB,aAAa,CAAC,CAAC;QACpB,OAAO,KAAK;MACd;MAEA,MAAMZ,IAAI,GAAG,MAAM,IAAI,CAACM,cAAc,CAAC,CAAC;MACxC,IAAI,CAACI,oBAAoB,CAACV,IAAI,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACU,aAAa,CAAC,CAAC;MACpB,OAAO,KAAK;IACd;EACF;AACF;;AAEA;AACA,MAAMkB,WAAW,GAAG,IAAI7C,WAAW,CAAC,CAAC;AACrC,eAAe6C,WAAW;;AAE1B;AACA,SAAS7C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}