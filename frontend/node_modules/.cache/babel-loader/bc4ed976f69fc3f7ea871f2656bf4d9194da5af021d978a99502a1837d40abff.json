{"ast": null, "code": "/**\n * Login Page\n * Handles login logic and state management\n */import React,{useState,useEffect}from'react';import{useNavigate,useLocation}from'react-router-dom';import LoginForm from'../components/auth.loginForm';import{authService}from'../../../services';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=()=>{const navigate=useNavigate();const location=useLocation();const[isLoading,setIsLoading]=useState(false);const[error,setError]=useState('');// Redirect if already authenticated\nuseEffect(()=>{if(authService.isAuthenticatedSync()){var _location$state,_location$state$from;const from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||'/dashboard';navigate(from,{replace:true});}},[navigate,location]);const handleLogin=async credentials=>{setIsLoading(true);setError('');try{var _location$state2,_location$state2$from;await authService.login(credentials);// Redirect to intended page or dashboard\nconst from=((_location$state2=location.state)===null||_location$state2===void 0?void 0:(_location$state2$from=_location$state2.from)===null||_location$state2$from===void 0?void 0:_location$state2$from.pathname)||'/dashboard';navigate(from,{replace:true});}catch(error){console.error('Login failed:',error);if(error.status===401){setError('Invalid email or password. Please try again.');}else if(error.status===0){setError('Unable to connect to server. Please check your internet connection.');}else{setError(error.message||'Login failed. Please try again.');}}finally{setIsLoading(false);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"sm:mx-auto sm:w-full sm:max-w-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",children:/*#__PURE__*/_jsx(LoginForm,{onSubmit:handleLogin,isLoading:isLoading,error:error})})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 border border-blue-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-blue-800 mb-2\",children:\"Demo Credentials\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-blue-700 space-y-1\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" <EMAIL>\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Password:\"}),\" demo123\"]})]})]})})]});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "LoginForm", "authService", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "navigate", "location", "isLoading", "setIsLoading", "error", "setError", "isAuthenticatedSync", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "handleLogin", "credentials", "_location$state2", "_location$state2$from", "login", "console", "status", "message", "className", "children", "onSubmit"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx"], "sourcesContent": ["/**\n * Login Page\n * Handles login logic and state management\n */\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport LoginForm from '../components/auth.loginForm';\nimport { authService } from '../../../services';\nimport { LoginRequest } from '../../../types';\n\nconst LoginPage: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (authService.isAuthenticatedSync()) {\n      const from = (location.state as any)?.from?.pathname || '/dashboard';\n      navigate(from, { replace: true });\n    }\n  }, [navigate, location]);\n\n  const handleLogin = async (credentials: LoginRequest) => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      await authService.login(credentials);\n      \n      // Redirect to intended page or dashboard\n      const from = (location.state as any)?.from?.pathname || '/dashboard';\n      navigate(from, { replace: true });\n      \n    } catch (error: any) {\n      console.error('Login failed:', error);\n      \n      if (error.status === 401) {\n        setError('Invalid email or password. Please try again.');\n      } else if (error.status === 0) {\n        setError('Unable to connect to server. Please check your internet connection.');\n      } else {\n        setError(error.message || 'Login failed. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <LoginForm\n            onSubmit={handleLogin}\n            isLoading={isLoading}\n            error={error}\n          />\n        </div>\n      </div>\n      \n      {/* Demo credentials info */}\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials</h3>\n          <div className=\"text-xs text-blue-700 space-y-1\">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> demo123</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,SAAS,KAAM,8BAA8B,CACpD,OAASC,WAAW,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGhD,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACU,SAAS,CAAEC,YAAY,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAS,EAAE,CAAC,CAE9C;AACAC,SAAS,CAAC,IAAM,CACd,GAAII,WAAW,CAACY,mBAAmB,CAAC,CAAC,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACrC,KAAM,CAAAC,IAAI,CAAG,EAAAF,eAAA,CAACN,QAAQ,CAACS,KAAK,UAAAH,eAAA,kBAAAC,oBAAA,CAAfD,eAAA,CAAyBE,IAAI,UAAAD,oBAAA,iBAA7BA,oBAAA,CAA+BG,QAAQ,GAAI,YAAY,CACpEX,QAAQ,CAACS,IAAI,CAAE,CAAEG,OAAO,CAAE,IAAK,CAAC,CAAC,CACnC,CACF,CAAC,CAAE,CAACZ,QAAQ,CAAEC,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAY,WAAW,CAAG,KAAO,CAAAC,WAAyB,EAAK,CACvDX,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,KAAAU,gBAAA,CAAAC,qBAAA,CACF,KAAM,CAAAtB,WAAW,CAACuB,KAAK,CAACH,WAAW,CAAC,CAEpC;AACA,KAAM,CAAAL,IAAI,CAAG,EAAAM,gBAAA,CAACd,QAAQ,CAACS,KAAK,UAAAK,gBAAA,kBAAAC,qBAAA,CAAfD,gBAAA,CAAyBN,IAAI,UAAAO,qBAAA,iBAA7BA,qBAAA,CAA+BL,QAAQ,GAAI,YAAY,CACpEX,QAAQ,CAACS,IAAI,CAAE,CAAEG,OAAO,CAAE,IAAK,CAAC,CAAC,CAEnC,CAAE,MAAOR,KAAU,CAAE,CACnBc,OAAO,CAACd,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CAErC,GAAIA,KAAK,CAACe,MAAM,GAAK,GAAG,CAAE,CACxBd,QAAQ,CAAC,8CAA8C,CAAC,CAC1D,CAAC,IAAM,IAAID,KAAK,CAACe,MAAM,GAAK,CAAC,CAAE,CAC7Bd,QAAQ,CAAC,qEAAqE,CAAC,CACjF,CAAC,IAAM,CACLA,QAAQ,CAACD,KAAK,CAACgB,OAAO,EAAI,iCAAiC,CAAC,CAC9D,CACF,CAAC,OAAS,CACRjB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,mBACEL,KAAA,QAAKuB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzF1B,IAAA,QAAKyB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/C1B,IAAA,QAAKyB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/D1B,IAAA,CAACH,SAAS,EACR8B,QAAQ,CAAEV,WAAY,CACtBX,SAAS,CAAEA,SAAU,CACrBE,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,CACH,CAAC,cAGNR,IAAA,QAAKyB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDxB,KAAA,QAAKuB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/D1B,IAAA,OAAIyB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC5ExB,KAAA,QAAKuB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CxB,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,QAAM,CAAQ,CAAC,oBAAiB,EAAG,CAAC,cAC/CxB,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,WAAS,CAAQ,CAAC,WAAQ,EAAG,CAAC,EACtC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}