{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import LoginPage from'./pages/auth/pages/auth.login';import SignupPage from'./pages/auth/pages/auth.signup';import{authService}from'./services';// Protected Route Component\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const isAuthenticated=authService.isAuthenticatedSync();if(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Public Route Component (redirect to dashboard if already authenticated)\nconst PublicRoute=_ref2=>{let{children}=_ref2;const isAuthenticated=authService.isAuthenticatedSync();if(isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Placeholder Dashboard Component\nconst Dashboard=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full space-y-8 p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold text-gray-900 mb-2\",children:\"Dashboard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Welcome to your dashboard!\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-8 rounded-lg shadow-md space-y-4\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>authService.logout(),className:\"w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700\",children:\"Logout\"})})]})});};function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/auth/login\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(LoginPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/auth/signup\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(SignupPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true})})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginPage", "SignupPage", "authService", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "isAuthenticated", "isAuthenticatedSync", "to", "replace", "PublicRoute", "_ref2", "Dashboard", "className", "onClick", "logout", "App", "path", "element"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport { authService } from './services';\n\n// Protected Route Component\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Public Route Component (redirect to dashboard if already authenticated)\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Placeholder Dashboard Component\nconst Dashboard: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md w-full space-y-8 p-4\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Dashboard\n          </h1>\n          <p className=\"text-gray-600\">\n            Welcome to your dashboard!\n          </p>\n        </div>\n        <div className=\"bg-white p-8 rounded-lg shadow-md space-y-4\">\n          <button\n            onClick={() => authService.logout()}\n            className=\"w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700\"\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        {/* Default route - redirect to login */}\n        <Route path=\"/\" element={<Navigate to=\"/auth/login\" replace />} />\n\n        {/* Auth routes */}\n        <Route\n          path=\"/auth/login\"\n          element={\n            <PublicRoute>\n              <LoginPage />\n            </PublicRoute>\n          }\n        />\n        <Route\n          path=\"/auth/signup\"\n          element={\n            <PublicRoute>\n              <SignupPage />\n            </PublicRoute>\n          }\n        />\n\n        {/* Protected routes */}\n        <Route\n          path=\"/dashboard\"\n          element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          }\n        />\n\n        {/* Catch all route - redirect to login */}\n        <Route path=\"*\" element={<Navigate to=\"/auth/login\" replace />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CACvD,OAASC,WAAW,KAAQ,YAAY,CAExC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,cAAuD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3E,KAAM,CAAAE,eAAe,CAAGV,WAAW,CAACW,mBAAmB,CAAC,CAAC,CAEzD,GAAI,CAACD,eAAe,CAAE,CACpB,mBAAOR,IAAA,CAACL,QAAQ,EAACe,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAC,CAC9C,CAEA,mBAAOX,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAK,WAAoD,CAAGC,KAAA,EAAkB,IAAjB,CAAEN,QAAS,CAAC,CAAAM,KAAA,CACxE,KAAM,CAAAL,eAAe,CAAGV,WAAW,CAACW,mBAAmB,CAAC,CAAC,CAEzD,GAAID,eAAe,CAAE,CACnB,mBAAOR,IAAA,CAACL,QAAQ,EAACe,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAC,CAC7C,CAEA,mBAAOX,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAO,SAAmB,CAAGA,CAAA,GAAM,CAChC,mBACEd,IAAA,QAAKe,SAAS,CAAC,0DAA0D,CAAAR,QAAA,cACvEH,KAAA,QAAKW,SAAS,CAAC,+BAA+B,CAAAR,QAAA,eAC5CH,KAAA,QAAKW,SAAS,CAAC,aAAa,CAAAR,QAAA,eAC1BP,IAAA,OAAIe,SAAS,CAAC,uCAAuC,CAAAR,QAAA,CAAC,WAEtD,CAAI,CAAC,cACLP,IAAA,MAAGe,SAAS,CAAC,eAAe,CAAAR,QAAA,CAAC,4BAE7B,CAAG,CAAC,EACD,CAAC,cACNP,IAAA,QAAKe,SAAS,CAAC,6CAA6C,CAAAR,QAAA,cAC1DP,IAAA,WACEgB,OAAO,CAAEA,CAAA,GAAMlB,WAAW,CAACmB,MAAM,CAAC,CAAE,CACpCF,SAAS,CAAC,iEAAiE,CAAAR,QAAA,CAC5E,QAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,QAAS,CAAAW,GAAGA,CAAA,CAAG,CACb,mBACElB,IAAA,CAACR,MAAM,EAAAe,QAAA,cACLH,KAAA,CAACX,MAAM,EAAAc,QAAA,eAELP,IAAA,CAACN,KAAK,EAACyB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEpB,IAAA,CAACL,QAAQ,EAACe,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAGlEX,IAAA,CAACN,KAAK,EACJyB,IAAI,CAAC,aAAa,CAClBC,OAAO,cACLpB,IAAA,CAACY,WAAW,EAAAL,QAAA,cACVP,IAAA,CAACJ,SAAS,GAAE,CAAC,CACF,CACd,CACF,CAAC,cACFI,IAAA,CAACN,KAAK,EACJyB,IAAI,CAAC,cAAc,CACnBC,OAAO,cACLpB,IAAA,CAACY,WAAW,EAAAL,QAAA,cACVP,IAAA,CAACH,UAAU,GAAE,CAAC,CACH,CACd,CACF,CAAC,cAGFG,IAAA,CAACN,KAAK,EACJyB,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLpB,IAAA,CAACK,cAAc,EAAAE,QAAA,cACbP,IAAA,CAACc,SAAS,GAAE,CAAC,CACC,CACjB,CACF,CAAC,cAGFd,IAAA,CAACN,KAAK,EAACyB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEpB,IAAA,CAACL,QAAQ,EAACe,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAC5D,CAAC,CACH,CAAC,CAEb,CAEA,cAAe,CAAAO,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}