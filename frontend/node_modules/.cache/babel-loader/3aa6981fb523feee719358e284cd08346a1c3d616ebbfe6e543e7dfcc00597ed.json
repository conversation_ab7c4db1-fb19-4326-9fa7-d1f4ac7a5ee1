{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v.5\",\n  key: \"1dkoa9\"\n}], [\"path\", {\n  d: \"M12 10v4h4\",\n  key: \"1czhmt\"\n}], [\"path\", {\n  d: \"m12 14 1.535-1.605a5 5 0 0 1 8 1.5\",\n  key: \"lvuxfi\"\n}], [\"path\", {\n  d: \"M22 22v-4h-4\",\n  key: \"1ewp4q\"\n}], [\"path\", {\n  d: \"m22 18-1.535 1.605a5 5 0 0 1-8-1.5\",\n  key: \"14ync0\"\n}]];\nconst FolderSync = createLucideIcon(\"folder-sync\", __iconNode);\nexport { __iconNode, FolderSync as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FolderSync", "createLucideIcon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/lucide-react/src/icons/folder-sync.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v.5',\n      key: '1dkoa9',\n    },\n  ],\n  ['path', { d: 'M12 10v4h4', key: '1czhmt' }],\n  ['path', { d: 'm12 14 1.535-1.605a5 5 0 0 1 8 1.5', key: 'lvuxfi' }],\n  ['path', { d: 'M22 22v-4h-4', key: '1ewp4q' }],\n  ['path', { d: 'm22 18-1.535 1.605a5 5 0 0 1-8-1.5', key: '14ync0' }],\n];\n\n/**\n * @component @name FolderSync\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAyMEg0YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDMuOWEyIDIgMCAwIDEgMS42OS45bC44MSAxLjJhMiAyIDAgMCAwIDEuNjcuOUgyMGEyIDIgMCAwIDEgMiAydi41IiAvPgogIDxwYXRoIGQ9Ik0xMiAxMHY0aDQiIC8+CiAgPHBhdGggZD0ibTEyIDE0IDEuNTM1LTEuNjA1YTUgNSAwIDAgMSA4IDEuNSIgLz4KICA8cGF0aCBkPSJNMjIgMjJ2LTRoLTQiIC8+CiAgPHBhdGggZD0ibTIyIDE4LTEuNTM1IDEuNjA1YTUgNSAwIDAgMS04LTEuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/folder-sync\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderSync = createLucideIcon('folder-sync', __iconNode);\n\nexport default FolderSync;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAU,GACrE;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}