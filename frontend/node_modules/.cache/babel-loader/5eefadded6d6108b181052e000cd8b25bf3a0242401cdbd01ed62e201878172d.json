{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import LoginPage from'./pages/auth/pages/auth.login';import SignupPage from'./pages/auth/pages/auth.signup';import DashboardPage from'./pages/dashboard/DashboardPage';import PlaygroundPage from'./pages/playground/PlaygroundPage';import CTAPage from'./pages/cta/CTAPage';import{PublicLayout,ProtectedLayout}from'./layouts';import{authService}from'./services';// Public Route Component (redirect to dashboard if already authenticated)\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PublicRoute=_ref=>{let{children}=_ref;const isAuthenticated=authService.isAuthenticatedSync();if(isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}return/*#__PURE__*/_jsx(PublicLayout,{children:children});};// Protected Route Component with Layout\nconst ProtectedRouteWithLayout=_ref2=>{let{children}=_ref2;const isAuthenticated=authService.isAuthenticatedSync();if(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true});}return/*#__PURE__*/_jsx(ProtectedLayout,{children:children});};function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/auth/login\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(LoginPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/auth/signup\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(SignupPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRouteWithLayout,{children:/*#__PURE__*/_jsx(DashboardPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/playground\",element:/*#__PURE__*/_jsx(ProtectedRouteWithLayout,{children:/*#__PURE__*/_jsx(PlaygroundPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/cta\",element:/*#__PURE__*/_jsx(ProtectedRouteWithLayout,{children:/*#__PURE__*/_jsx(CTAPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/auth/login\",replace:true})})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginPage", "SignupPage", "DashboardPage", "PlaygroundPage", "CTAPage", "PublicLayout", "ProtectedLayout", "authService", "jsx", "_jsx", "jsxs", "_jsxs", "PublicRoute", "_ref", "children", "isAuthenticated", "isAuthenticatedSync", "to", "replace", "ProtectedRouteWithLayout", "_ref2", "App", "path", "element"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport LoginPage from './pages/auth/pages/auth.login';\nimport SignupPage from './pages/auth/pages/auth.signup';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport PlaygroundPage from './pages/playground/PlaygroundPage';\nimport CTAPage from './pages/cta/CTAPage';\nimport { PublicLayout, ProtectedLayout } from './layouts';\nimport { authService } from './services';\n\n// Public Route Component (redirect to dashboard if already authenticated)\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <PublicLayout>{children}</PublicLayout>;\n};\n\n// Protected Route Component with Layout\nconst ProtectedRouteWithLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = authService.isAuthenticatedSync();\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  return <ProtectedLayout>{children}</ProtectedLayout>;\n};\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        {/* Default route - redirect to login */}\n        <Route path=\"/\" element={<Navigate to=\"/auth/login\" replace />} />\n\n        {/* Auth routes */}\n        <Route\n          path=\"/auth/login\"\n          element={\n            <PublicRoute>\n              <LoginPage />\n            </PublicRoute>\n          }\n        />\n        <Route\n          path=\"/auth/signup\"\n          element={\n            <PublicRoute>\n              <SignupPage />\n            </PublicRoute>\n          }\n        />\n\n        {/* Protected routes */}\n        <Route\n          path=\"/dashboard\"\n          element={\n            <ProtectedRouteWithLayout>\n              <DashboardPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n        <Route\n          path=\"/playground\"\n          element={\n            <ProtectedRouteWithLayout>\n              <PlaygroundPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n        <Route\n          path=\"/cta\"\n          element={\n            <ProtectedRouteWithLayout>\n              <CTAPage />\n            </ProtectedRouteWithLayout>\n          }\n        />\n\n        {/* Catch all route - redirect to login */}\n        <Route path=\"*\" element={<Navigate to=\"/auth/login\" replace />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CACvD,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAC9D,MAAO,CAAAC,OAAO,KAAM,qBAAqB,CACzC,OAASC,YAAY,CAAEC,eAAe,KAAQ,WAAW,CACzD,OAASC,WAAW,KAAQ,YAAY,CAExC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,WAAoD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACxE,KAAM,CAAAE,eAAe,CAAGR,WAAW,CAACS,mBAAmB,CAAC,CAAC,CAEzD,GAAID,eAAe,CAAE,CACnB,mBAAON,IAAA,CAACV,QAAQ,EAACkB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAC,CAC7C,CAEA,mBAAOT,IAAA,CAACJ,YAAY,EAAAS,QAAA,CAAEA,QAAQ,CAAe,CAAC,CAChD,CAAC,CAED;AACA,KAAM,CAAAK,wBAAiE,CAAGC,KAAA,EAAkB,IAAjB,CAAEN,QAAS,CAAC,CAAAM,KAAA,CACrF,KAAM,CAAAL,eAAe,CAAGR,WAAW,CAACS,mBAAmB,CAAC,CAAC,CAEzD,GAAI,CAACD,eAAe,CAAE,CACpB,mBAAON,IAAA,CAACV,QAAQ,EAACkB,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAC,CAC9C,CAEA,mBAAOT,IAAA,CAACH,eAAe,EAAAQ,QAAA,CAAEA,QAAQ,CAAkB,CAAC,CACtD,CAAC,CAED,QAAS,CAAAO,GAAGA,CAAA,CAAG,CACb,mBACEZ,IAAA,CAACb,MAAM,EAAAkB,QAAA,cACLH,KAAA,CAACd,MAAM,EAAAiB,QAAA,eAELL,IAAA,CAACX,KAAK,EAACwB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACV,QAAQ,EAACkB,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAGlET,IAAA,CAACX,KAAK,EACJwB,IAAI,CAAC,aAAa,CAClBC,OAAO,cACLd,IAAA,CAACG,WAAW,EAAAE,QAAA,cACVL,IAAA,CAACT,SAAS,GAAE,CAAC,CACF,CACd,CACF,CAAC,cACFS,IAAA,CAACX,KAAK,EACJwB,IAAI,CAAC,cAAc,CACnBC,OAAO,cACLd,IAAA,CAACG,WAAW,EAAAE,QAAA,cACVL,IAAA,CAACR,UAAU,GAAE,CAAC,CACH,CACd,CACF,CAAC,cAGFQ,IAAA,CAACX,KAAK,EACJwB,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLd,IAAA,CAACU,wBAAwB,EAAAL,QAAA,cACvBL,IAAA,CAACP,aAAa,GAAE,CAAC,CACO,CAC3B,CACF,CAAC,cACFO,IAAA,CAACX,KAAK,EACJwB,IAAI,CAAC,aAAa,CAClBC,OAAO,cACLd,IAAA,CAACU,wBAAwB,EAAAL,QAAA,cACvBL,IAAA,CAACN,cAAc,GAAE,CAAC,CACM,CAC3B,CACF,CAAC,cACFM,IAAA,CAACX,KAAK,EACJwB,IAAI,CAAC,MAAM,CACXC,OAAO,cACLd,IAAA,CAACU,wBAAwB,EAAAL,QAAA,cACvBL,IAAA,CAACL,OAAO,GAAE,CAAC,CACa,CAC3B,CACF,CAAC,cAGFK,IAAA,CAACX,KAAK,EAACwB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEd,IAAA,CAACV,QAAQ,EAACkB,EAAE,CAAC,aAAa,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAC5D,CAAC,CACH,CAAC,CAEb,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}