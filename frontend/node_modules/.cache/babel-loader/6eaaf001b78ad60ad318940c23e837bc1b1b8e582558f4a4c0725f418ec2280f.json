{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"gxeob9\"\n}], [\"path\", {\n  d: \"m9 20 3-6 3 6\",\n  key: \"se2kox\"\n}], [\"path\", {\n  d: \"m6 8 6 2 6-2\",\n  key: \"4o3us4\"\n}], [\"path\", {\n  d: \"M12 10v4\",\n  key: \"1kjpxc\"\n}]];\nconst PersonStanding = createLucideIcon(\"person-standing\", __iconNode);\nexport { __iconNode, PersonStanding as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "PersonStanding", "createLucideIcon"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/node_modules/lucide-react/src/icons/person-standing.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '5', r: '1', key: 'gxeob9' }],\n  ['path', { d: 'm9 20 3-6 3 6', key: 'se2kox' }],\n  ['path', { d: 'm6 8 6 2 6-2', key: '4o3us4' }],\n  ['path', { d: 'M12 10v4', key: '1kjpxc' }],\n];\n\n/**\n * @component @name PersonStanding\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjUiIHI9IjEiIC8+CiAgPHBhdGggZD0ibTkgMjAgMy02IDMgNiIgLz4KICA8cGF0aCBkPSJtNiA4IDYgMiA2LTIiIC8+CiAgPHBhdGggZD0iTTEyIDEwdjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/person-standing\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PersonStanding = createLucideIcon('person-standing', __iconNode);\n\nexport default PersonStanding;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAE,cAAA,GAAiBC,gBAAiB,oBAAmBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}