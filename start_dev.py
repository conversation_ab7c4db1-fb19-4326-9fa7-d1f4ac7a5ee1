#!/usr/bin/env python3
"""
Development server startup script
Runs both backend and frontend concurrently
"""
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def run_backend():
    """Run the FastAPI backend"""
    print("🚀 Starting Backend Server...")
    backend_env = os.environ.copy()
    backend_env['PYTHONPATH'] = str(Path.cwd())
    
    return subprocess.Popen([
        sys.executable, "-m", "uvicorn", "backend.main:app",
        "--reload", "--host", "0.0.0.0", "--port", "8000"
    ], env=backend_env)

def run_frontend():
    """Run the React frontend"""
    print("🎨 Starting Frontend Server...")
    frontend_dir = Path.cwd() / "frontend"
    
    return subprocess.Popen([
        "npm", "start"
    ], cwd=frontend_dir)

def main():
    """Main function to run both servers"""
    print("🔥 Starting Development Environment...")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("backend").exists() or not Path("frontend").exists():
        print("❌ Error: Please run this script from the project root directory")
        print("   Expected structure:")
        print("   ├── backend/")
        print("   ├── frontend/")
        print("   └── start_dev.py")
        sys.exit(1)
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("⚠️  Warning: .env file not found. Creating from template...")
        if Path(".env.example").exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
            print("   Please edit .env with your actual API keys")
        else:
            print("❌ No .env.example found. Please create .env manually")
    
    processes = []
    
    try:
        # Start backend
        backend_process = run_backend()
        processes.append(backend_process)
        time.sleep(2)  # Give backend time to start
        
        # Start frontend
        frontend_process = run_frontend()
        processes.append(frontend_process)
        
        print("\n" + "=" * 50)
        print("🎉 Development servers started!")
        print("📱 Frontend: http://localhost:3000")
        print("🚀 Backend:  http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("=" * 50)
        print("\n💡 Press Ctrl+C to stop all servers")
        
        # Wait for processes
        while True:
            time.sleep(1)
            
            # Check if any process has died
            for i, process in enumerate(processes):
                if process.poll() is not None:
                    print(f"\n❌ Process {i} has stopped unexpectedly")
                    return
                    
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        
    finally:
        # Clean up processes
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        print("✅ All servers stopped")

if __name__ == "__main__":
    main()
