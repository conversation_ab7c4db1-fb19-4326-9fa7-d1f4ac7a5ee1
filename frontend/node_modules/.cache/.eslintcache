[{"/home/<USER>/Documents/nextai/langraph_test/frontend/src/index.tsx": "1", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx": "3", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/index.ts": "4", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Button.tsx": "5", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Input.tsx": "6", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx": "7", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.signup.tsx": "8", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/index.ts": "9", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/baseHttp.ts": "10", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts": "11", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts": "12", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/bookingService.ts": "13", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/chatService.ts": "14", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/components/auth.loginForm.tsx": "15"}, {"size": 554, "mtime": 1751435435232, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1751435435232, "results": "18", "hashOfConfig": "17"}, {"size": 2610, "mtime": 1751437925460, "results": "19", "hashOfConfig": "17"}, {"size": 192, "mtime": 1751437124215, "results": "20", "hashOfConfig": "17"}, {"size": 2064, "mtime": 1751436287909, "results": "21", "hashOfConfig": "17"}, {"size": 2897, "mtime": 1751436303424, "results": "22", "hashOfConfig": "17"}, {"size": 2518, "mtime": 1751437820343, "results": "23", "hashOfConfig": "17"}, {"size": 7814, "mtime": 1751437829938, "results": "24", "hashOfConfig": "17"}, {"size": 549, "mtime": 1751435766748, "results": "25", "hashOfConfig": "17"}, {"size": 5497, "mtime": 1751437737932, "results": "26", "hashOfConfig": "17"}, {"size": 4932, "mtime": 1751437666293, "results": "27", "hashOfConfig": "17"}, {"size": 8284, "mtime": 1751436270365, "results": "28", "hashOfConfig": "17"}, {"size": 8177, "mtime": 1751436234402, "results": "29", "hashOfConfig": "17"}, {"size": 6829, "mtime": 1751435757818, "results": "30", "hashOfConfig": "17"}, {"size": 4491, "mtime": 1751436332108, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4pi4tb", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Documents/nextai/langraph_test/frontend/src/index.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/index.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Button.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Input.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.signup.tsx", ["77", "78"], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/index.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/baseHttp.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/bookingService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/chatService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/components/auth.loginForm.tsx", ["79"], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 212, "column": 17, "nodeType": "82", "endLine": 212, "endColumn": 81}, {"ruleId": "80", "severity": 1, "message": "81", "line": 216, "column": 17, "nodeType": "82", "endLine": 216, "endColumn": 81}, {"ruleId": "80", "severity": 1, "message": "81", "line": 130, "column": 13, "nodeType": "82", "endLine": 133, "endColumn": 14}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]