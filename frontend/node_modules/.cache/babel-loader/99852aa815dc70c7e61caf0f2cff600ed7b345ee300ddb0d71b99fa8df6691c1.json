{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/playground/PlaygroundPage.tsx\",\n  _s = $RefreshSig$();\n/**\n * Playground Page\n * Interactive chat testing interface\n */\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Send, Bot, User, Trash2, Download, Settings, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlaygroundPage = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: '1',\n    content: 'Hello! I\\'m your AI assistant. How can I help you today?',\n    type: 'assistant',\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      content: inputMessage,\n      type: 'user',\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Simulate AI response\n    setTimeout(() => {\n      const assistantMessage = {\n        id: (Date.now() + 1).toString(),\n        content: `I received your message: \"${inputMessage}\". This is a demo response from the AI assistant. In a real implementation, this would connect to your chat API.`,\n        type: 'assistant',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, assistantMessage]);\n      setIsLoading(false);\n    }, 1000 + Math.random() * 2000);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const clearChat = () => {\n    setMessages([{\n      id: '1',\n      content: 'Hello! I\\'m your AI assistant. How can I help you today?',\n      type: 'assistant',\n      timestamp: new Date()\n    }]);\n  };\n  const exportChat = () => {\n    const chatData = messages.map(msg => ({\n      timestamp: msg.timestamp.toISOString(),\n      type: msg.type,\n      content: msg.content\n    }));\n    const blob = new Blob([JSON.stringify(chatData, null, 2)], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b border-gray-200 px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(Zap, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"AI Playground\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Test and interact with the AI assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: exportChat,\n            className: \"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Export\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearChat,\n            className: \"flex items-center space-x-2 px-3 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Clear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(Settings, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full max-w-4xl mx-auto flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-start space-x-3 animate-fade-in-up ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`,\n            style: {\n              animationDelay: `${index * 100}ms`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${message.type === 'user' ? 'bg-blue-500' : 'bg-gradient-to-r from-purple-500 to-pink-500'}`,\n              children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(User, {\n                className: \"h-4 w-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Bot, {\n                className: \"h-4 w-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex-1 max-w-xs sm:max-w-md lg:max-w-lg ${message.type === 'user' ? 'text-right' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `inline-block p-3 rounded-2xl ${message.type === 'user' ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200 text-gray-900 shadow-sm'}`,\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm whitespace-pre-wrap\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: message.timestamp.toLocaleTimeString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, message.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3 animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Bot, {\n                className: \"h-4 w-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white border border-gray-200 rounded-2xl p-3 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: '0.1s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200 bg-white p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: inputMessage,\n                onChange: e => setInputMessage(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: \"Type your message here... (Press Enter to send)\",\n                className: \"w-full resize-none border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                rows: 1,\n                style: {\n                  minHeight: '40px',\n                  maxHeight: '120px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSendMessage,\n              disabled: !inputMessage.trim() || isLoading,\n              className: \"flex-shrink-0 bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Send, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(PlaygroundPage, \"9i+/iJbZipHkm4QCN9FN5q/q6Ws=\");\n_c = PlaygroundPage;\nexport default PlaygroundPage;\nvar _c;\n$RefreshReg$(_c, \"PlaygroundPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Send", "Bot", "User", "Trash2", "Download", "Settings", "Zap", "jsxDEV", "_jsxDEV", "PlaygroundPage", "_s", "messages", "setMessages", "id", "content", "type", "timestamp", "Date", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "userMessage", "now", "toString", "prev", "setTimeout", "assistant<PERSON><PERSON><PERSON>", "Math", "random", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "clearChat", "exportChat", "chatData", "map", "msg", "toISOString", "blob", "Blob", "JSON", "stringify", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "split", "click", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "message", "index", "style", "animationDelay", "toLocaleTimeString", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "minHeight", "maxHeight", "disabled", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/playground/PlaygroundPage.tsx"], "sourcesContent": ["/**\n * Playground Page\n * Interactive chat testing interface\n */\nimport React, { useState, useRef, useEffect } from 'react';\nimport { \n  Send, \n  Bot, \n  User, \n  Trash2, \n  Download, \n  Settings,\n  Zap,\n  MessageCircle\n} from 'lucide-react';\n\ninterface Message {\n  id: string;\n  content: string;\n  type: 'user' | 'assistant';\n  timestamp: Date;\n}\n\nconst PlaygroundPage: React.FC = () => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'Hello! I\\'m your AI assistant. How can I help you today?',\n      type: 'assistant',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: inputMessage,\n      type: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Simulate AI response\n    setTimeout(() => {\n      const assistantMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        content: `I received your message: \"${inputMessage}\". This is a demo response from the AI assistant. In a real implementation, this would connect to your chat API.`,\n        type: 'assistant',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, assistantMessage]);\n      setIsLoading(false);\n    }, 1000 + Math.random() * 2000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const clearChat = () => {\n    setMessages([{\n      id: '1',\n      content: 'Hello! I\\'m your AI assistant. How can I help you today?',\n      type: 'assistant',\n      timestamp: new Date()\n    }]);\n  };\n\n  const exportChat = () => {\n    const chatData = messages.map(msg => ({\n      timestamp: msg.timestamp.toISOString(),\n      type: msg.type,\n      content: msg.content\n    }));\n    \n    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\">\n              <Zap className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">AI Playground</h1>\n              <p className=\"text-sm text-gray-600\">Test and interact with the AI assistant</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={exportChat}\n              className=\"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Export</span>\n            </button>\n            <button\n              onClick={clearChat}\n              className=\"flex items-center space-x-2 px-3 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Clear</span>\n            </button>\n            <button className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\">\n              <Settings className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Chat Area */}\n      <div className=\"flex-1 overflow-hidden\">\n        <div className=\"h-full max-w-4xl mx-auto flex flex-col\">\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-6 space-y-4\">\n            {messages.map((message, index) => (\n              <div\n                key={message.id}\n                className={`flex items-start space-x-3 animate-fade-in-up ${\n                  message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n                }`}\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n                  message.type === 'user' \n                    ? 'bg-blue-500' \n                    : 'bg-gradient-to-r from-purple-500 to-pink-500'\n                }`}>\n                  {message.type === 'user' ? (\n                    <User className=\"h-4 w-4 text-white\" />\n                  ) : (\n                    <Bot className=\"h-4 w-4 text-white\" />\n                  )}\n                </div>\n                \n                <div className={`flex-1 max-w-xs sm:max-w-md lg:max-w-lg ${\n                  message.type === 'user' ? 'text-right' : ''\n                }`}>\n                  <div className={`inline-block p-3 rounded-2xl ${\n                    message.type === 'user'\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-white border border-gray-200 text-gray-900 shadow-sm'\n                  }`}>\n                    <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                  </div>\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    {message.timestamp.toLocaleTimeString()}\n                  </p>\n                </div>\n              </div>\n            ))}\n            \n            {isLoading && (\n              <div className=\"flex items-start space-x-3 animate-fade-in\">\n                <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center\">\n                  <Bot className=\"h-4 w-4 text-white\" />\n                </div>\n                <div className=\"bg-white border border-gray-200 rounded-2xl p-3 shadow-sm\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input Area */}\n          <div className=\"border-t border-gray-200 bg-white p-4\">\n            <div className=\"flex items-end space-x-3\">\n              <div className=\"flex-1\">\n                <textarea\n                  value={inputMessage}\n                  onChange={(e) => setInputMessage(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  placeholder=\"Type your message here... (Press Enter to send)\"\n                  className=\"w-full resize-none border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  rows={1}\n                  style={{ minHeight: '40px', maxHeight: '120px' }}\n                />\n              </div>\n              <button\n                onClick={handleSendMessage}\n                disabled={!inputMessage.trim() || isLoading}\n                className=\"flex-shrink-0 bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                <Send className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PlaygroundPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,GAAG,QAEE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAStB,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAY,CAClD;IACEgB,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,0DAA0D;IACnEC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMyB,cAAc,GAAGxB,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAMyB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED5B,SAAS,CAAC,MAAM;IACdwB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMiB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC,CAAC,IAAIT,SAAS,EAAE;IAEvC,MAAMU,WAAoB,GAAG;MAC3BjB,EAAE,EAAEI,IAAI,CAACc,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBlB,OAAO,EAAEI,YAAY;MACrBH,IAAI,EAAE,MAAM;MACZC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,WAAW,CAAC,CAAC;IAC3CX,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAa,UAAU,CAAC,MAAM;MACf,MAAMC,gBAAyB,GAAG;QAChCtB,EAAE,EAAE,CAACI,IAAI,CAACc,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;QAC/BlB,OAAO,EAAE,6BAA6BI,YAAY,kHAAkH;QACpKH,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDL,WAAW,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,gBAAgB,CAAC,CAAC;MAChDd,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,GAAGe,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;EACjC,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBd,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,SAAS,GAAGA,CAAA,KAAM;IACtB/B,WAAW,CAAC,CAAC;MACXC,EAAE,EAAE,GAAG;MACPC,OAAO,EAAE,0DAA0D;MACnEC,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,QAAQ,GAAGlC,QAAQ,CAACmC,GAAG,CAACC,GAAG,KAAK;MACpC/B,SAAS,EAAE+B,GAAG,CAAC/B,SAAS,CAACgC,WAAW,CAAC,CAAC;MACtCjC,IAAI,EAAEgC,GAAG,CAAChC,IAAI;MACdD,OAAO,EAAEiC,GAAG,CAACjC;IACf,CAAC,CAAC,CAAC;IAEH,MAAMmC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACP,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MAAE9B,IAAI,EAAE;IAAmB,CAAC,CAAC;IACxF,MAAMsC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrC,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,eAAe,IAAI3C,IAAI,CAAC,CAAC,CAAC+B,WAAW,CAAC,CAAC,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IACzEL,CAAC,CAACM,KAAK,CAAC,CAAC;IACTR,GAAG,CAACS,eAAe,CAACV,GAAG,CAAC;EAC1B,CAAC;EAED,oBACE7C,OAAA;IAAKwD,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAEhDzD,OAAA;MAAKwD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DzD,OAAA;QAAKwD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzD,OAAA;UAAKwD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzD,OAAA;YAAKwD,SAAS,EAAC,6DAA6D;YAAAC,QAAA,eAC1EzD,OAAA,CAACF,GAAG;cAAC0D,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN7D,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAIwD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE7D,OAAA;cAAGwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7D,OAAA;UAAKwD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzD,OAAA;YACE8D,OAAO,EAAE1B,UAAW;YACpBoB,SAAS,EAAC,wHAAwH;YAAAC,QAAA,gBAElIzD,OAAA,CAACJ,QAAQ;cAAC4D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC7D,OAAA;cAAMwD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACT7D,OAAA;YACE8D,OAAO,EAAE3B,SAAU;YACnBqB,SAAS,EAAC,oHAAoH;YAAAC,QAAA,gBAE9HzD,OAAA,CAACL,MAAM;cAAC6D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7D,OAAA;cAAMwD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACT7D,OAAA;YAAQwD,SAAS,EAAC,sFAAsF;YAAAC,QAAA,eACtGzD,OAAA,CAACH,QAAQ;cAAC2D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCzD,OAAA;QAAKwD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDzD,OAAA;UAAKwD,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAClDtD,QAAQ,CAACmC,GAAG,CAAC,CAACyB,OAAO,EAAEC,KAAK,kBAC3BhE,OAAA;YAEEwD,SAAS,EAAE,iDACTO,OAAO,CAACxD,IAAI,KAAK,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAChE;YACH0D,KAAK,EAAE;cAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;YAAK,CAAE;YAAAP,QAAA,gBAE9CzD,OAAA;cAAKwD,SAAS,EAAE,uEACdO,OAAO,CAACxD,IAAI,KAAK,MAAM,GACnB,aAAa,GACb,8CAA8C,EACjD;cAAAkD,QAAA,EACAM,OAAO,CAACxD,IAAI,KAAK,MAAM,gBACtBP,OAAA,CAACN,IAAI;gBAAC8D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEvC7D,OAAA,CAACP,GAAG;gBAAC+D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACtC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAE,2CACdO,OAAO,CAACxD,IAAI,KAAK,MAAM,GAAG,YAAY,GAAG,EAAE,EAC1C;cAAAkD,QAAA,gBACDzD,OAAA;gBAAKwD,SAAS,EAAE,gCACdO,OAAO,CAACxD,IAAI,KAAK,MAAM,GACnB,wBAAwB,GACxB,yDAAyD,EAC5D;gBAAAkD,QAAA,eACDzD,OAAA;kBAAGwD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEM,OAAO,CAACzD;gBAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN7D,OAAA;gBAAGwD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCM,OAAO,CAACvD,SAAS,CAAC2D,kBAAkB,CAAC;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GA/BDE,OAAO,CAAC1D,EAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCZ,CACN,CAAC,EAEDjD,SAAS,iBACRZ,OAAA;YAAKwD,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDzD,OAAA;cAAKwD,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAC/HzD,OAAA,CAACP,GAAG;gBAAC+D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,2DAA2D;cAAAC,QAAA,eACxEzD,OAAA;gBAAKwD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzD,OAAA;kBAAKwD,SAAS,EAAC;gBAAiD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE7D,OAAA;kBAAKwD,SAAS,EAAC,iDAAiD;kBAACS,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1G7D,OAAA;kBAAKwD,SAAS,EAAC,iDAAiD;kBAACS,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7D,OAAA;YAAKoE,GAAG,EAAEtD;UAAe;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDzD,OAAA;YAAKwD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCzD,OAAA;cAAKwD,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBzD,OAAA;gBACEqE,KAAK,EAAE3D,YAAa;gBACpB4D,QAAQ,EAAGvC,CAAC,IAAKpB,eAAe,CAACoB,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;gBACjDG,UAAU,EAAE1C,cAAe;gBAC3B2C,WAAW,EAAC,iDAAiD;gBAC7DjB,SAAS,EAAC,0HAA0H;gBACpIkB,IAAI,EAAE,CAAE;gBACRT,KAAK,EAAE;kBAAEU,SAAS,EAAE,MAAM;kBAAEC,SAAS,EAAE;gBAAQ;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7D,OAAA;cACE8D,OAAO,EAAE1C,iBAAkB;cAC3ByD,QAAQ,EAAE,CAACnE,YAAY,CAACW,IAAI,CAAC,CAAC,IAAIT,SAAU;cAC5C4C,SAAS,EAAC,yIAAyI;cAAAC,QAAA,eAEnJzD,OAAA,CAACR,IAAI;gBAACgE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA5MID,cAAwB;AAAA6E,EAAA,GAAxB7E,cAAwB;AA8M9B,eAAeA,cAAc;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}