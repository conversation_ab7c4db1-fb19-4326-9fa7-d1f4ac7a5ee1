{"ast": null, "code": "export{default as PublicLayout}from'./PublicLayout';export{default as ProtectedLayout}from'./ProtectedLayout';", "map": {"version": 3, "names": ["default", "PublicLayout", "ProtectedLayout"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/index.ts"], "sourcesContent": ["export { default as PublicLayout } from './PublicLayout';\nexport { default as ProtectedLayout } from './ProtectedLayout';\n"], "mappings": "AAAA,OAASA,OAAO,GAAI,CAAAC,YAAY,KAAQ,gBAAgB,CACxD,OAASD,OAAO,GAAI,CAAAE,eAAe,KAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}