/**
 * Base HTTP service using Axios
 * Handles authentication, interceptors, and common configurations
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// Extend the Axios config interface to include metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: Date;
    };
  }
}

class BaseHttpService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      withCredentials: true, // Include cookies in requests
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Add request timestamp for debugging
        config.metadata = { startTime: new Date() };

        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });

        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        const duration = new Date().getTime() - (response.config.metadata?.startTime?.getTime() || 0);
        
        console.log(`✅ ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
          data: response.data,
        });

        return response;
      },
      (error) => {
        const duration = error.config?.metadata?.startTime
          ? new Date().getTime() - error.config.metadata.startTime.getTime()
          : 0;

        console.error(`❌ ${error.response?.status || 'Network'} ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
          error: error.response?.data || error.message,
        });

        // Handle specific error cases
        if (error.response?.status === 401) {
          this.handleUnauthorized();
        }

        return Promise.reject(this.formatError(error));
      }
    );
  }

  private handleUnauthorized(): void {
    // Clear auth data
    localStorage.removeItem('user_data');

    // Redirect to login if not already there
    if (window.location.pathname !== '/auth/login') {
      window.location.href = '/auth/login';
    }
  }

  private formatError(error: any): any {
    if (error.response) {
      // Server responded with error status
      return {
        status: error.response.status,
        message: error.response.data?.detail || error.response.data?.message || 'Server error',
        data: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        status: 0,
        message: 'Network error - please check your connection',
        data: null,
      };
    } else {
      // Something else happened
      return {
        status: 0,
        message: error.message || 'Unknown error occurred',
        data: null,
      };
    }
  }

  // HTTP Methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get<T>(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.put<T>(url, data, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.patch<T>(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }

  // Utility methods (kept for compatibility but not used in session-based auth)
  setAuthToken(token: string): void {
    // No-op for session-based auth
  }

  clearAuthToken(): void {
    localStorage.removeItem('user_data');
  }

  getBaseURL(): string {
    return this.baseURL;
  }

  // File upload helper
  async uploadFile<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    };

    const response = await this.api.post<T>(url, formData, config);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.get('/health');
  }
}

// Create and export singleton instance
const baseHttp = new BaseHttpService();
export default baseHttp;

// Export the class for testing purposes
export { BaseHttpService };
