[{"/home/<USER>/Documents/nextai/langraph_test/frontend/src/index.tsx": "1", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx": "3", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/index.ts": "4", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Button.tsx": "5", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Input.tsx": "6", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx": "7", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.signup.tsx": "8", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/index.ts": "9", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/baseHttp.ts": "10", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts": "11", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts": "12", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/bookingService.ts": "13", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/chatService.ts": "14", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/components/auth.loginForm.tsx": "15", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/index.ts": "16", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/PublicLayout.tsx": "17", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/ProtectedLayout.tsx": "18", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/dashboard/DashboardPage.tsx": "19", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/cta/CTAPage.tsx": "20", "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/playground/PlaygroundPage.tsx": "21"}, {"size": 554, "mtime": 1751435435232, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1751435435232, "results": "24", "hashOfConfig": "23"}, {"size": 2532, "mtime": 1751438873456, "results": "25", "hashOfConfig": "23"}, {"size": 192, "mtime": 1751437124215, "results": "26", "hashOfConfig": "23"}, {"size": 2064, "mtime": 1751436287909, "results": "27", "hashOfConfig": "23"}, {"size": 2897, "mtime": 1751436303424, "results": "28", "hashOfConfig": "23"}, {"size": 2518, "mtime": 1751437820343, "results": "29", "hashOfConfig": "23"}, {"size": 7814, "mtime": 1751437829938, "results": "30", "hashOfConfig": "23"}, {"size": 549, "mtime": 1751435766748, "results": "31", "hashOfConfig": "23"}, {"size": 5497, "mtime": 1751437737932, "results": "32", "hashOfConfig": "23"}, {"size": 4932, "mtime": 1751437666293, "results": "33", "hashOfConfig": "23"}, {"size": 8284, "mtime": 1751436270365, "results": "34", "hashOfConfig": "23"}, {"size": 8177, "mtime": 1751436234402, "results": "35", "hashOfConfig": "23"}, {"size": 6829, "mtime": 1751435757818, "results": "36", "hashOfConfig": "23"}, {"size": 4491, "mtime": 1751436332108, "results": "37", "hashOfConfig": "23"}, {"size": 122, "mtime": 1751438665579, "results": "38", "hashOfConfig": "23"}, {"size": 2376, "mtime": 1751438632273, "results": "39", "hashOfConfig": "23"}, {"size": 5468, "mtime": 1751438657081, "results": "40", "hashOfConfig": "23"}, {"size": 6955, "mtime": 1751438703203, "results": "41", "hashOfConfig": "23"}, {"size": 9828, "mtime": 1751438795103, "results": "42", "hashOfConfig": "23"}, {"size": 8360, "mtime": 1751438745507, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4pi4tb", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Documents/nextai/langraph_test/frontend/src/index.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/App.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/index.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Button.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/components/Input.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.signup.tsx", ["107", "108"], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/index.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/baseHttp.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/authService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/bookingService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/chatService.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/components/auth.loginForm.tsx", ["109"], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/index.ts", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/PublicLayout.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/layouts/ProtectedLayout.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/dashboard/DashboardPage.tsx", [], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/cta/CTAPage.tsx", ["110"], [], "/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/playground/PlaygroundPage.tsx", ["111"], [], {"ruleId": "112", "severity": 1, "message": "113", "line": 212, "column": 17, "nodeType": "114", "endLine": 212, "endColumn": 81}, {"ruleId": "112", "severity": 1, "message": "113", "line": 216, "column": 17, "nodeType": "114", "endLine": 216, "endColumn": 81}, {"ruleId": "112", "severity": 1, "message": "113", "line": 130, "column": 13, "nodeType": "114", "endLine": 133, "endColumn": 14}, {"ruleId": "115", "severity": 1, "message": "116", "line": 11, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 11, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "119", "line": 14, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 14, "endColumn": 16}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'Mail' is defined but never used.", "Identifier", "unusedVar", "'MessageCircle' is defined but never used."]