{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx\",\n  _s = $RefreshSig$();\n/**\n * Login Page\n * Handles login logic and state management\n */\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport LoginForm from '../components/auth.loginForm';\nimport { authService } from '../../../services';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (authService.isAuthenticatedSync()) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [navigate, location]);\n  const handleLogin = async credentials => {\n    setIsLoading(true);\n    setError('');\n    try {\n      var _location$state2, _location$state2$from;\n      await authService.login(credentials);\n\n      // Redirect to intended page or dashboard\n      const from = ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : (_location$state2$from = _location$state2.from) === null || _location$state2$from === void 0 ? void 0 : _location$state2$from.pathname) || '/dashboard';\n      navigate(from, {\n        replace: true\n      });\n    } catch (error) {\n      console.error('Login failed:', error);\n      if (error.status === 401) {\n        setError('Invalid email or password. Please try again.');\n      } else if (error.status === 0) {\n        setError('Unable to connect to server. Please check your internet connection.');\n      } else {\n        setError(error.message || 'Login failed. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: /*#__PURE__*/_jsxDEV(LoginForm, {\n          onSubmit: handleLogin,\n          isLoading: isLoading,\n          error: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-blue-800 mb-2\",\n          children: \"Demo Credentials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-blue-700 space-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 16\n            }, this), \" <EMAIL>\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Password:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 16\n            }, this), \" demo123\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"JErxIDXSevHTByyH6UNclsrjBww=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "LoginForm", "authService", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "navigate", "location", "isLoading", "setIsLoading", "error", "setError", "isAuthenticatedSync", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "handleLogin", "credentials", "_location$state2", "_location$state2$from", "login", "console", "status", "message", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/pages/auth/pages/auth.login.tsx"], "sourcesContent": ["/**\n * Login Page\n * Handles login logic and state management\n */\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport LoginForm from '../components/auth.loginForm';\nimport { authService } from '../../../services';\nimport { LoginRequest } from '../../../types';\n\nconst LoginPage: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (authService.isAuthenticatedSync()) {\n      const from = (location.state as any)?.from?.pathname || '/dashboard';\n      navigate(from, { replace: true });\n    }\n  }, [navigate, location]);\n\n  const handleLogin = async (credentials: LoginRequest) => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      await authService.login(credentials);\n      \n      // Redirect to intended page or dashboard\n      const from = (location.state as any)?.from?.pathname || '/dashboard';\n      navigate(from, { replace: true });\n      \n    } catch (error: any) {\n      console.error('Login failed:', error);\n      \n      if (error.status === 401) {\n        setError('Invalid email or password. Please try again.');\n      } else if (error.status === 0) {\n        setError('Unable to connect to server. Please check your internet connection.');\n      } else {\n        setError(error.message || 'Login failed. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <LoginForm\n            onSubmit={handleLogin}\n            isLoading={isLoading}\n            error={error}\n          />\n        </div>\n      </div>\n      \n      {/* Demo credentials info */}\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials</h3>\n          <div className=\"text-xs text-blue-700 space-y-1\">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> demo123</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,SAAS,MAAM,8BAA8B;AACpD,SAASC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGhD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAS,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,IAAII,WAAW,CAACW,mBAAmB,CAAC,CAAC,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACrC,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAACN,QAAQ,CAACS,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBE,IAAI,cAAAD,oBAAA,uBAA7BA,oBAAA,CAA+BG,QAAQ,KAAI,YAAY;MACpEX,QAAQ,CAACS,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACZ,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExB,MAAMY,WAAW,GAAG,MAAOC,WAAyB,IAAK;IACvDX,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAU,gBAAA,EAAAC,qBAAA;MACF,MAAMrB,WAAW,CAACsB,KAAK,CAACH,WAAW,CAAC;;MAEpC;MACA,MAAML,IAAI,GAAG,EAAAM,gBAAA,GAACd,QAAQ,CAACS,KAAK,cAAAK,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAyBN,IAAI,cAAAO,qBAAA,uBAA7BA,qBAAA,CAA+BL,QAAQ,KAAI,YAAY;MACpEX,QAAQ,CAACS,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IAEnC,CAAC,CAAC,OAAOR,KAAU,EAAE;MACnBc,OAAO,CAACd,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MAErC,IAAIA,KAAK,CAACe,MAAM,KAAK,GAAG,EAAE;QACxBd,QAAQ,CAAC,8CAA8C,CAAC;MAC1D,CAAC,MAAM,IAAID,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;QAC7Bd,QAAQ,CAAC,qEAAqE,CAAC;MACjF,CAAC,MAAM;QACLA,QAAQ,CAACD,KAAK,CAACgB,OAAO,IAAI,iCAAiC,CAAC;MAC9D;IACF,CAAC,SAAS;MACRjB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEN,OAAA;IAAKwB,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzFzB,OAAA;MAAKwB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CzB,OAAA;QAAKwB,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DzB,OAAA,CAACH,SAAS;UACR6B,QAAQ,EAAEV,WAAY;UACtBX,SAAS,EAAEA,SAAU;UACrBE,KAAK,EAAEA;QAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDzB,OAAA;QAAKwB,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DzB,OAAA;UAAIwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E9B,OAAA;UAAKwB,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CzB,OAAA;YAAAyB,QAAA,gBAAGzB,OAAA;cAAAyB,QAAA,EAAQ;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qBAAiB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C9B,OAAA;YAAAyB,QAAA,gBAAGzB,OAAA;cAAAyB,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,YAAQ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAjEID,SAAmB;EAAA,QACNN,WAAW,EACXC,WAAW;AAAA;AAAAmC,EAAA,GAFxB9B,SAAmB;AAmEzB,eAAeA,SAAS;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}