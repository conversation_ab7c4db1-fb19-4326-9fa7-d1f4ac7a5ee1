{"ast": null, "code": "/**\n * Dashboard service\n * Handles dashboard analytics, system metrics, and admin functionality\n */import baseHttp from'./baseHttp';class DashboardService{constructor(){this.DASHBOARD_ENDPOINTS={STATS:'/api/dashboard/stats',ANALYTICS:'/api/dashboard/analytics',METRICS:'/api/dashboard/metrics',USERS:'/api/dashboard/users',BOOKINGS_OVERVIEW:'/api/dashboard/bookings/overview'};}/**\n   * Get dashboard statistics\n   */async getDashboardStats(){try{return await baseHttp.get(this.DASHBOARD_ENDPOINTS.STATS);}catch(error){console.error('Failed to get dashboard stats:',error);throw error;}}/**\n   * Get analytics summary\n   */async getAnalyticsSummary(){let days=arguments.length>0&&arguments[0]!==undefined?arguments[0]:7;try{return await baseHttp.get(this.DASHBOARD_ENDPOINTS.ANALYTICS,{params:{days}});}catch(error){console.error('Failed to get analytics summary:',error);throw error;}}/**\n   * Get system metrics\n   */async getSystemMetrics(){try{return await baseHttp.get(this.DASHBOARD_ENDPOINTS.METRICS);}catch(error){console.error('Failed to get system metrics:',error);throw error;}}/**\n   * Get users overview\n   */async getUsersOverview(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:50;try{return await baseHttp.get(this.DASHBOARD_ENDPOINTS.USERS,{params:{limit}});}catch(error){console.error('Failed to get users overview:',error);throw error;}}/**\n   * Get bookings overview\n   */async getBookingsOverview(){let days=arguments.length>0&&arguments[0]!==undefined?arguments[0]:7;try{return await baseHttp.get(this.DASHBOARD_ENDPOINTS.BOOKINGS_OVERVIEW,{params:{days}});}catch(error){console.error('Failed to get bookings overview:',error);throw error;}}/**\n   * Format analytics data for charts\n   */formatAnalyticsForChart(analytics){var _analytics$message_an,_analytics$message_an2,_analytics$message_an3,_analytics$booking_an,_analytics$booking_an2,_analytics$user_analy,_analytics$user_analy2;const sentimentData=[{name:'Positive',value:((_analytics$message_an=analytics.message_analytics)===null||_analytics$message_an===void 0?void 0:_analytics$message_an.positive_messages)||0},{name:'Negative',value:((_analytics$message_an2=analytics.message_analytics)===null||_analytics$message_an2===void 0?void 0:_analytics$message_an2.negative_messages)||0},{name:'Neutral',value:((_analytics$message_an3=analytics.message_analytics)===null||_analytics$message_an3===void 0?void 0:_analytics$message_an3.neutral_messages)||0}];const bookingData=[{name:'Confirmed',value:((_analytics$booking_an=analytics.booking_analytics)===null||_analytics$booking_an===void 0?void 0:_analytics$booking_an.confirmed_bookings)||0},{name:'Cancelled',value:((_analytics$booking_an2=analytics.booking_analytics)===null||_analytics$booking_an2===void 0?void 0:_analytics$booking_an2.cancelled_bookings)||0}];const userActivityData=[{name:'New Users',value:((_analytics$user_analy=analytics.user_analytics)===null||_analytics$user_analy===void 0?void 0:_analytics$user_analy.new_users)||0},{name:'Active Users',value:((_analytics$user_analy2=analytics.user_analytics)===null||_analytics$user_analy2===void 0?void 0:_analytics$user_analy2.active_users)||0}];return{sentimentData,bookingData,userActivityData};}/**\n   * Format daily stats for line chart\n   */formatDailyStatsForChart(dailyStats){return dailyStats.map(stat=>({date:stat._id,total:stat.count,confirmed:stat.confirmed,cancelled:stat.cancelled}));}/**\n   * Calculate growth percentage\n   */calculateGrowth(current,previous){if(previous===0){return{percentage:current>0?100:0,isPositive:current>0,formatted:current>0?'+100%':'0%'};}const percentage=(current-previous)/previous*100;const isPositive=percentage>=0;const formatted=\"\".concat(isPositive?'+':'').concat(percentage.toFixed(1),\"%\");return{percentage:Math.abs(percentage),isPositive,formatted};}/**\n   * Get status indicators\n   */getStatusIndicators(metrics){return[{name:'Database',status:metrics.database_status==='connected'?'healthy':'error',value:metrics.database_status,description:'MongoDB connection status'},{name:'Collections',status:Object.keys(metrics.collections_info||{}).length>0?'healthy':'warning',value:\"\".concat(Object.keys(metrics.collections_info||{}).length,\" collections\"),description:'Database collections status'},{name:'Performance',status:'healthy',// Could implement actual performance checks\nvalue:'Good',description:'System performance metrics'}];}/**\n   * Export analytics data as CSV\n   */exportAnalyticsCSV(analytics){var _analytics$message_an4,_analytics$message_an5,_analytics$message_an6,_analytics$message_an7,_analytics$booking_an3,_analytics$booking_an4,_analytics$booking_an5,_analytics$user_analy3,_analytics$user_analy4;const csvData=[['Metric','Value'],['Date Range',\"\".concat(analytics.date_range.start,\" to \").concat(analytics.date_range.end)],['Total Messages',((_analytics$message_an4=analytics.message_analytics)===null||_analytics$message_an4===void 0?void 0:_analytics$message_an4.total_messages)||0],['Positive Messages',((_analytics$message_an5=analytics.message_analytics)===null||_analytics$message_an5===void 0?void 0:_analytics$message_an5.positive_messages)||0],['Negative Messages',((_analytics$message_an6=analytics.message_analytics)===null||_analytics$message_an6===void 0?void 0:_analytics$message_an6.negative_messages)||0],['Neutral Messages',((_analytics$message_an7=analytics.message_analytics)===null||_analytics$message_an7===void 0?void 0:_analytics$message_an7.neutral_messages)||0],['Total Bookings',((_analytics$booking_an3=analytics.booking_analytics)===null||_analytics$booking_an3===void 0?void 0:_analytics$booking_an3.total_bookings)||0],['Confirmed Bookings',((_analytics$booking_an4=analytics.booking_analytics)===null||_analytics$booking_an4===void 0?void 0:_analytics$booking_an4.confirmed_bookings)||0],['Cancelled Bookings',((_analytics$booking_an5=analytics.booking_analytics)===null||_analytics$booking_an5===void 0?void 0:_analytics$booking_an5.cancelled_bookings)||0],['New Users',((_analytics$user_analy3=analytics.user_analytics)===null||_analytics$user_analy3===void 0?void 0:_analytics$user_analy3.new_users)||0],['Active Users',((_analytics$user_analy4=analytics.user_analytics)===null||_analytics$user_analy4===void 0?void 0:_analytics$user_analy4.active_users)||0]];const csvContent=csvData.map(row=>row.join(',')).join('\\n');const blob=new Blob([csvContent],{type:'text/csv'});const url=URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=\"analytics_\".concat(analytics.date_range.start,\"_to_\").concat(analytics.date_range.end,\".csv\");document.body.appendChild(link);link.click();document.body.removeChild(link);URL.revokeObjectURL(url);}/**\n   * Format large numbers for display\n   */formatNumber(num){if(num>=1000000){return(num/1000000).toFixed(1)+'M';}else if(num>=1000){return(num/1000).toFixed(1)+'K';}return num.toString();}/**\n   * Get time period options for analytics\n   */getTimePeriodOptions(){return[{value:1,label:'Last 24 hours'},{value:7,label:'Last 7 days'},{value:14,label:'Last 14 days'},{value:30,label:'Last 30 days'}];}/**\n   * Generate dashboard summary text\n   */generateSummaryText(stats){return\"\\nDashboard Summary\\n\\nTotal Users: \".concat(this.formatNumber(stats.total_users),\"\\nTotal Messages: \").concat(this.formatNumber(stats.total_messages),\"\\nTotal Bookings: \").concat(this.formatNumber(stats.total_bookings),\"\\nActive Sessions: \").concat(this.formatNumber(stats.active_sessions),\"\\n\\nToday's Activity:\\n- New Bookings: \").concat(stats.today_bookings,\"\\n- New Messages: \").concat(stats.today_messages,\"\\n\\nGenerated: \").concat(new Date().toLocaleString(),\"\\n    \").trim();}}// Create and export singleton instance\nconst dashboardService=new DashboardService();export default dashboardService;// Export the class for testing purposes\nexport{DashboardService};", "map": {"version": 3, "names": ["baseHttp", "DashboardService", "constructor", "DASHBOARD_ENDPOINTS", "STATS", "ANALYTICS", "METRICS", "USERS", "BOOKINGS_OVERVIEW", "getDashboardStats", "get", "error", "console", "getAnalyticsSummary", "days", "arguments", "length", "undefined", "params", "getSystemMetrics", "getUsersOverview", "limit", "getBookingsOverview", "formatAnalyticsForChart", "analytics", "_analytics$message_an", "_analytics$message_an2", "_analytics$message_an3", "_analytics$booking_an", "_analytics$booking_an2", "_analytics$user_analy", "_analytics$user_analy2", "sentimentData", "name", "value", "message_analytics", "positive_messages", "negative_messages", "neutral_messages", "bookingData", "booking_analytics", "confirmed_bookings", "cancelled_bookings", "userActivityData", "user_analytics", "new_users", "active_users", "formatDailyStatsForChart", "dailyStats", "map", "stat", "date", "_id", "total", "count", "confirmed", "cancelled", "calculateGrowth", "current", "previous", "percentage", "isPositive", "formatted", "concat", "toFixed", "Math", "abs", "getStatusIndicators", "metrics", "status", "database_status", "description", "Object", "keys", "collections_info", "exportAnalyticsCSV", "_analytics$message_an4", "_analytics$message_an5", "_analytics$message_an6", "_analytics$message_an7", "_analytics$booking_an3", "_analytics$booking_an4", "_analytics$booking_an5", "_analytics$user_analy3", "_analytics$user_analy4", "csvData", "date_range", "start", "end", "total_messages", "total_bookings", "csv<PERSON><PERSON>nt", "row", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "formatNumber", "num", "toString", "getTimePeriodOptions", "label", "generateSummaryText", "stats", "total_users", "active_sessions", "today_bookings", "today_messages", "Date", "toLocaleString", "trim", "dashboardService"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts"], "sourcesContent": ["/**\n * Dashboard service\n * Handles dashboard analytics, system metrics, and admin functionality\n */\nimport baseHttp from './baseHttp';\nimport { \n  DashboardStats, \n  AnalyticsSummary, \n  SystemMetrics,\n  User,\n  Booking \n} from '../types';\n\nclass DashboardService {\n  private readonly DASHBOARD_ENDPOINTS = {\n    STATS: '/api/dashboard/stats',\n    ANALYTICS: '/api/dashboard/analytics',\n    METRICS: '/api/dashboard/metrics',\n    USERS: '/api/dashboard/users',\n    BOOKINGS_OVERVIEW: '/api/dashboard/bookings/overview',\n  };\n\n  /**\n   * Get dashboard statistics\n   */\n  async getDashboardStats(): Promise<DashboardStats> {\n    try {\n      return await baseHttp.get<DashboardStats>(this.DASHBOARD_ENDPOINTS.STATS);\n    } catch (error) {\n      console.error('Failed to get dashboard stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get analytics summary\n   */\n  async getAnalyticsSummary(days: number = 7): Promise<AnalyticsSummary> {\n    try {\n      return await baseHttp.get<AnalyticsSummary>(this.DASHBOARD_ENDPOINTS.ANALYTICS, {\n        params: { days }\n      });\n    } catch (error) {\n      console.error('Failed to get analytics summary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get system metrics\n   */\n  async getSystemMetrics(): Promise<SystemMetrics> {\n    try {\n      return await baseHttp.get<SystemMetrics>(this.DASHBOARD_ENDPOINTS.METRICS);\n    } catch (error) {\n      console.error('Failed to get system metrics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get users overview\n   */\n  async getUsersOverview(limit: number = 50): Promise<{ users: User[]; total: number; limit: number }> {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.USERS, {\n        params: { limit }\n      });\n    } catch (error) {\n      console.error('Failed to get users overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get bookings overview\n   */\n  async getBookingsOverview(days: number = 7): Promise<{\n    bookings: Booking[];\n    daily_stats: Array<{\n      _id: string;\n      count: number;\n      confirmed: number;\n      cancelled: number;\n    }>;\n    total: number;\n    date_range: {\n      start: string;\n      end: string;\n      days: number;\n    };\n  }> {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.BOOKINGS_OVERVIEW, {\n        params: { days }\n      });\n    } catch (error) {\n      console.error('Failed to get bookings overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Format analytics data for charts\n   */\n  formatAnalyticsForChart(analytics: AnalyticsSummary): {\n    sentimentData: Array<{ name: string; value: number }>;\n    bookingData: Array<{ name: string; value: number }>;\n    userActivityData: Array<{ name: string; value: number }>;\n  } {\n    const sentimentData = [\n      { name: 'Positive', value: analytics.message_analytics?.positive_messages || 0 },\n      { name: 'Negative', value: analytics.message_analytics?.negative_messages || 0 },\n      { name: 'Neutral', value: analytics.message_analytics?.neutral_messages || 0 },\n    ];\n\n    const bookingData = [\n      { name: 'Confirmed', value: analytics.booking_analytics?.confirmed_bookings || 0 },\n      { name: 'Cancelled', value: analytics.booking_analytics?.cancelled_bookings || 0 },\n    ];\n\n    const userActivityData = [\n      { name: 'New Users', value: analytics.user_analytics?.new_users || 0 },\n      { name: 'Active Users', value: analytics.user_analytics?.active_users || 0 },\n    ];\n\n    return {\n      sentimentData,\n      bookingData,\n      userActivityData,\n    };\n  }\n\n  /**\n   * Format daily stats for line chart\n   */\n  formatDailyStatsForChart(dailyStats: Array<{\n    _id: string;\n    count: number;\n    confirmed: number;\n    cancelled: number;\n  }>): Array<{\n    date: string;\n    total: number;\n    confirmed: number;\n    cancelled: number;\n  }> {\n    return dailyStats.map(stat => ({\n      date: stat._id,\n      total: stat.count,\n      confirmed: stat.confirmed,\n      cancelled: stat.cancelled,\n    }));\n  }\n\n  /**\n   * Calculate growth percentage\n   */\n  calculateGrowth(current: number, previous: number): {\n    percentage: number;\n    isPositive: boolean;\n    formatted: string;\n  } {\n    if (previous === 0) {\n      return {\n        percentage: current > 0 ? 100 : 0,\n        isPositive: current > 0,\n        formatted: current > 0 ? '+100%' : '0%',\n      };\n    }\n\n    const percentage = ((current - previous) / previous) * 100;\n    const isPositive = percentage >= 0;\n    const formatted = `${isPositive ? '+' : ''}${percentage.toFixed(1)}%`;\n\n    return {\n      percentage: Math.abs(percentage),\n      isPositive,\n      formatted,\n    };\n  }\n\n  /**\n   * Get status indicators\n   */\n  getStatusIndicators(metrics: SystemMetrics): Array<{\n    name: string;\n    status: 'healthy' | 'warning' | 'error';\n    value: string;\n    description: string;\n  }> {\n    return [\n      {\n        name: 'Database',\n        status: metrics.database_status === 'connected' ? 'healthy' : 'error',\n        value: metrics.database_status,\n        description: 'MongoDB connection status',\n      },\n      {\n        name: 'Collections',\n        status: Object.keys(metrics.collections_info || {}).length > 0 ? 'healthy' : 'warning',\n        value: `${Object.keys(metrics.collections_info || {}).length} collections`,\n        description: 'Database collections status',\n      },\n      {\n        name: 'Performance',\n        status: 'healthy', // Could implement actual performance checks\n        value: 'Good',\n        description: 'System performance metrics',\n      },\n    ];\n  }\n\n  /**\n   * Export analytics data as CSV\n   */\n  exportAnalyticsCSV(analytics: AnalyticsSummary): void {\n    const csvData = [\n      ['Metric', 'Value'],\n      ['Date Range', `${analytics.date_range.start} to ${analytics.date_range.end}`],\n      ['Total Messages', analytics.message_analytics?.total_messages || 0],\n      ['Positive Messages', analytics.message_analytics?.positive_messages || 0],\n      ['Negative Messages', analytics.message_analytics?.negative_messages || 0],\n      ['Neutral Messages', analytics.message_analytics?.neutral_messages || 0],\n      ['Total Bookings', analytics.booking_analytics?.total_bookings || 0],\n      ['Confirmed Bookings', analytics.booking_analytics?.confirmed_bookings || 0],\n      ['Cancelled Bookings', analytics.booking_analytics?.cancelled_bookings || 0],\n      ['New Users', analytics.user_analytics?.new_users || 0],\n      ['Active Users', analytics.user_analytics?.active_users || 0],\n    ];\n\n    const csvContent = csvData.map(row => row.join(',')).join('\\n');\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `analytics_${analytics.date_range.start}_to_${analytics.date_range.end}.csv`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    URL.revokeObjectURL(url);\n  }\n\n  /**\n   * Format large numbers for display\n   */\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  /**\n   * Get time period options for analytics\n   */\n  getTimePeriodOptions(): Array<{ value: number; label: string }> {\n    return [\n      { value: 1, label: 'Last 24 hours' },\n      { value: 7, label: 'Last 7 days' },\n      { value: 14, label: 'Last 14 days' },\n      { value: 30, label: 'Last 30 days' },\n    ];\n  }\n\n  /**\n   * Generate dashboard summary text\n   */\n  generateSummaryText(stats: DashboardStats): string {\n    return `\nDashboard Summary\n\nTotal Users: ${this.formatNumber(stats.total_users)}\nTotal Messages: ${this.formatNumber(stats.total_messages)}\nTotal Bookings: ${this.formatNumber(stats.total_bookings)}\nActive Sessions: ${this.formatNumber(stats.active_sessions)}\n\nToday's Activity:\n- New Bookings: ${stats.today_bookings}\n- New Messages: ${stats.today_messages}\n\nGenerated: ${new Date().toLocaleString()}\n    `.trim();\n  }\n}\n\n// Create and export singleton instance\nconst dashboardService = new DashboardService();\nexport default dashboardService;\n\n// Export the class for testing purposes\nexport { DashboardService };\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,QAAQ,KAAM,YAAY,CASjC,KAAM,CAAAC,gBAAiB,CAAAC,YAAA,OACJC,mBAAmB,CAAG,CACrCC,KAAK,CAAE,sBAAsB,CAC7BC,SAAS,CAAE,0BAA0B,CACrCC,OAAO,CAAE,wBAAwB,CACjCC,KAAK,CAAE,sBAAsB,CAC7BC,iBAAiB,CAAE,kCACrB,CAAC,EAED;AACF;AACA,KACE,KAAM,CAAAC,iBAAiBA,CAAA,CAA4B,CACjD,GAAI,CACF,MAAO,MAAM,CAAAT,QAAQ,CAACU,GAAG,CAAiB,IAAI,CAACP,mBAAmB,CAACC,KAAK,CAAC,CAC3E,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAE,mBAAmBA,CAAA,CAA8C,IAA7C,CAAAC,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CACxC,GAAI,CACF,MAAO,MAAM,CAAAf,QAAQ,CAACU,GAAG,CAAmB,IAAI,CAACP,mBAAmB,CAACE,SAAS,CAAE,CAC9Ea,MAAM,CAAE,CAAEJ,IAAK,CACjB,CAAC,CAAC,CACJ,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAQ,gBAAgBA,CAAA,CAA2B,CAC/C,GAAI,CACF,MAAO,MAAM,CAAAnB,QAAQ,CAACU,GAAG,CAAgB,IAAI,CAACP,mBAAmB,CAACG,OAAO,CAAC,CAC5E,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAS,gBAAgBA,CAAA,CAA+E,IAA9E,CAAAC,KAAa,CAAAN,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACvC,GAAI,CACF,MAAO,MAAM,CAAAf,QAAQ,CAACU,GAAG,CAAC,IAAI,CAACP,mBAAmB,CAACI,KAAK,CAAE,CACxDW,MAAM,CAAE,CAAEG,KAAM,CAClB,CAAC,CAAC,CACJ,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAW,mBAAmBA,CAAA,CActB,IAduB,CAAAR,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAexC,GAAI,CACF,MAAO,MAAM,CAAAf,QAAQ,CAACU,GAAG,CAAC,IAAI,CAACP,mBAAmB,CAACK,iBAAiB,CAAE,CACpEU,MAAM,CAAE,CAAEJ,IAAK,CACjB,CAAC,CAAC,CACJ,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACEY,uBAAuBA,CAACC,SAA2B,CAIjD,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CACA,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEC,IAAI,CAAE,UAAU,CAAEC,KAAK,CAAE,EAAAT,qBAAA,CAAAD,SAAS,CAACW,iBAAiB,UAAAV,qBAAA,iBAA3BA,qBAAA,CAA6BW,iBAAiB,GAAI,CAAE,CAAC,CAChF,CAAEH,IAAI,CAAE,UAAU,CAAEC,KAAK,CAAE,EAAAR,sBAAA,CAAAF,SAAS,CAACW,iBAAiB,UAAAT,sBAAA,iBAA3BA,sBAAA,CAA6BW,iBAAiB,GAAI,CAAE,CAAC,CAChF,CAAEJ,IAAI,CAAE,SAAS,CAAEC,KAAK,CAAE,EAAAP,sBAAA,CAAAH,SAAS,CAACW,iBAAiB,UAAAR,sBAAA,iBAA3BA,sBAAA,CAA6BW,gBAAgB,GAAI,CAAE,CAAC,CAC/E,CAED,KAAM,CAAAC,WAAW,CAAG,CAClB,CAAEN,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,EAAAN,qBAAA,CAAAJ,SAAS,CAACgB,iBAAiB,UAAAZ,qBAAA,iBAA3BA,qBAAA,CAA6Ba,kBAAkB,GAAI,CAAE,CAAC,CAClF,CAAER,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,EAAAL,sBAAA,CAAAL,SAAS,CAACgB,iBAAiB,UAAAX,sBAAA,iBAA3BA,sBAAA,CAA6Ba,kBAAkB,GAAI,CAAE,CAAC,CACnF,CAED,KAAM,CAAAC,gBAAgB,CAAG,CACvB,CAAEV,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,EAAAJ,qBAAA,CAAAN,SAAS,CAACoB,cAAc,UAAAd,qBAAA,iBAAxBA,qBAAA,CAA0Be,SAAS,GAAI,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,cAAc,CAAEC,KAAK,CAAE,EAAAH,sBAAA,CAAAP,SAAS,CAACoB,cAAc,UAAAb,sBAAA,iBAAxBA,sBAAA,CAA0Be,YAAY,GAAI,CAAE,CAAC,CAC7E,CAED,MAAO,CACLd,aAAa,CACbO,WAAW,CACXI,gBACF,CAAC,CACH,CAEA;AACF;AACA,KACEI,wBAAwBA,CAACC,UAKvB,CAKC,CACD,MAAO,CAAAA,UAAU,CAACC,GAAG,CAACC,IAAI,GAAK,CAC7BC,IAAI,CAAED,IAAI,CAACE,GAAG,CACdC,KAAK,CAAEH,IAAI,CAACI,KAAK,CACjBC,SAAS,CAAEL,IAAI,CAACK,SAAS,CACzBC,SAAS,CAAEN,IAAI,CAACM,SAClB,CAAC,CAAC,CAAC,CACL,CAEA;AACF;AACA,KACEC,eAAeA,CAACC,OAAe,CAAEC,QAAgB,CAI/C,CACA,GAAIA,QAAQ,GAAK,CAAC,CAAE,CAClB,MAAO,CACLC,UAAU,CAAEF,OAAO,CAAG,CAAC,CAAG,GAAG,CAAG,CAAC,CACjCG,UAAU,CAAEH,OAAO,CAAG,CAAC,CACvBI,SAAS,CAAEJ,OAAO,CAAG,CAAC,CAAG,OAAO,CAAG,IACrC,CAAC,CACH,CAEA,KAAM,CAAAE,UAAU,CAAI,CAACF,OAAO,CAAGC,QAAQ,EAAIA,QAAQ,CAAI,GAAG,CAC1D,KAAM,CAAAE,UAAU,CAAGD,UAAU,EAAI,CAAC,CAClC,KAAM,CAAAE,SAAS,IAAAC,MAAA,CAAMF,UAAU,CAAG,GAAG,CAAG,EAAE,EAAAE,MAAA,CAAGH,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,KAAG,CAErE,MAAO,CACLJ,UAAU,CAAEK,IAAI,CAACC,GAAG,CAACN,UAAU,CAAC,CAChCC,UAAU,CACVC,SACF,CAAC,CACH,CAEA;AACF;AACA,KACEK,mBAAmBA,CAACC,OAAsB,CAKvC,CACD,MAAO,CACL,CACEnC,IAAI,CAAE,UAAU,CAChBoC,MAAM,CAAED,OAAO,CAACE,eAAe,GAAK,WAAW,CAAG,SAAS,CAAG,OAAO,CACrEpC,KAAK,CAAEkC,OAAO,CAACE,eAAe,CAC9BC,WAAW,CAAE,2BACf,CAAC,CACD,CACEtC,IAAI,CAAE,aAAa,CACnBoC,MAAM,CAAEG,MAAM,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,EAAI,CAAC,CAAC,CAAC,CAAC1D,MAAM,CAAG,CAAC,CAAG,SAAS,CAAG,SAAS,CACtFkB,KAAK,IAAA6B,MAAA,CAAKS,MAAM,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,EAAI,CAAC,CAAC,CAAC,CAAC1D,MAAM,gBAAc,CAC1EuD,WAAW,CAAE,6BACf,CAAC,CACD,CACEtC,IAAI,CAAE,aAAa,CACnBoC,MAAM,CAAE,SAAS,CAAE;AACnBnC,KAAK,CAAE,MAAM,CACbqC,WAAW,CAAE,4BACf,CAAC,CACF,CACH,CAEA;AACF;AACA,KACEI,kBAAkBA,CAACnD,SAA2B,CAAQ,KAAAoD,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpD,KAAM,CAAAC,OAAO,CAAG,CACd,CAAC,QAAQ,CAAE,OAAO,CAAC,CACnB,CAAC,YAAY,IAAAtB,MAAA,CAAKvC,SAAS,CAAC8D,UAAU,CAACC,KAAK,SAAAxB,MAAA,CAAOvC,SAAS,CAAC8D,UAAU,CAACE,GAAG,EAAG,CAC9E,CAAC,gBAAgB,CAAE,EAAAZ,sBAAA,CAAApD,SAAS,CAACW,iBAAiB,UAAAyC,sBAAA,iBAA3BA,sBAAA,CAA6Ba,cAAc,GAAI,CAAC,CAAC,CACpE,CAAC,mBAAmB,CAAE,EAAAZ,sBAAA,CAAArD,SAAS,CAACW,iBAAiB,UAAA0C,sBAAA,iBAA3BA,sBAAA,CAA6BzC,iBAAiB,GAAI,CAAC,CAAC,CAC1E,CAAC,mBAAmB,CAAE,EAAA0C,sBAAA,CAAAtD,SAAS,CAACW,iBAAiB,UAAA2C,sBAAA,iBAA3BA,sBAAA,CAA6BzC,iBAAiB,GAAI,CAAC,CAAC,CAC1E,CAAC,kBAAkB,CAAE,EAAA0C,sBAAA,CAAAvD,SAAS,CAACW,iBAAiB,UAAA4C,sBAAA,iBAA3BA,sBAAA,CAA6BzC,gBAAgB,GAAI,CAAC,CAAC,CACxE,CAAC,gBAAgB,CAAE,EAAA0C,sBAAA,CAAAxD,SAAS,CAACgB,iBAAiB,UAAAwC,sBAAA,iBAA3BA,sBAAA,CAA6BU,cAAc,GAAI,CAAC,CAAC,CACpE,CAAC,oBAAoB,CAAE,EAAAT,sBAAA,CAAAzD,SAAS,CAACgB,iBAAiB,UAAAyC,sBAAA,iBAA3BA,sBAAA,CAA6BxC,kBAAkB,GAAI,CAAC,CAAC,CAC5E,CAAC,oBAAoB,CAAE,EAAAyC,sBAAA,CAAA1D,SAAS,CAACgB,iBAAiB,UAAA0C,sBAAA,iBAA3BA,sBAAA,CAA6BxC,kBAAkB,GAAI,CAAC,CAAC,CAC5E,CAAC,WAAW,CAAE,EAAAyC,sBAAA,CAAA3D,SAAS,CAACoB,cAAc,UAAAuC,sBAAA,iBAAxBA,sBAAA,CAA0BtC,SAAS,GAAI,CAAC,CAAC,CACvD,CAAC,cAAc,CAAE,EAAAuC,sBAAA,CAAA5D,SAAS,CAACoB,cAAc,UAAAwC,sBAAA,iBAAxBA,sBAAA,CAA0BtC,YAAY,GAAI,CAAC,CAAC,CAC9D,CAED,KAAM,CAAA6C,UAAU,CAAGN,OAAO,CAACpC,GAAG,CAAC2C,GAAG,EAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAC/D,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACJ,UAAU,CAAC,CAAE,CAAEK,IAAI,CAAE,UAAW,CAAC,CAAC,CACzD,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGN,GAAG,CACfG,IAAI,CAACI,QAAQ,cAAAzC,MAAA,CAAgBvC,SAAS,CAAC8D,UAAU,CAACC,KAAK,SAAAxB,MAAA,CAAOvC,SAAS,CAAC8D,UAAU,CAACE,GAAG,QAAM,CAC5Fa,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CAE/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAC1B,CAEA;AACF;AACA,KACEa,YAAYA,CAACC,GAAW,CAAU,CAChC,GAAIA,GAAG,EAAI,OAAO,CAAE,CAClB,MAAO,CAACA,GAAG,CAAG,OAAO,EAAE/C,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACzC,CAAC,IAAM,IAAI+C,GAAG,EAAI,IAAI,CAAE,CACtB,MAAO,CAACA,GAAG,CAAG,IAAI,EAAE/C,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACtC,CACA,MAAO,CAAA+C,GAAG,CAACC,QAAQ,CAAC,CAAC,CACvB,CAEA;AACF;AACA,KACEC,oBAAoBA,CAAA,CAA4C,CAC9D,MAAO,CACL,CAAE/E,KAAK,CAAE,CAAC,CAAEgF,KAAK,CAAE,eAAgB,CAAC,CACpC,CAAEhF,KAAK,CAAE,CAAC,CAAEgF,KAAK,CAAE,aAAc,CAAC,CAClC,CAAEhF,KAAK,CAAE,EAAE,CAAEgF,KAAK,CAAE,cAAe,CAAC,CACpC,CAAEhF,KAAK,CAAE,EAAE,CAAEgF,KAAK,CAAE,cAAe,CAAC,CACrC,CACH,CAEA;AACF;AACA,KACEC,mBAAmBA,CAACC,KAAqB,CAAU,CACjD,MAAO,uCAAArD,MAAA,CAGI,IAAI,CAAC+C,YAAY,CAACM,KAAK,CAACC,WAAW,CAAC,uBAAAtD,MAAA,CACjC,IAAI,CAAC+C,YAAY,CAACM,KAAK,CAAC3B,cAAc,CAAC,uBAAA1B,MAAA,CACvC,IAAI,CAAC+C,YAAY,CAACM,KAAK,CAAC1B,cAAc,CAAC,wBAAA3B,MAAA,CACtC,IAAI,CAAC+C,YAAY,CAACM,KAAK,CAACE,eAAe,CAAC,4CAAAvD,MAAA,CAGzCqD,KAAK,CAACG,cAAc,uBAAAxD,MAAA,CACpBqD,KAAK,CAACI,cAAc,oBAAAzD,MAAA,CAEzB,GAAI,CAAA0D,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,WAClCC,IAAI,CAAC,CAAC,CACV,CACF,CAEA;AACA,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAA3H,gBAAgB,CAAC,CAAC,CAC/C,cAAe,CAAA2H,gBAAgB,CAE/B;AACA,OAAS3H,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}