{"ast": null, "code": "/**\n * Dashboard service\n * Handles dashboard analytics, system metrics, and admin functionality\n */\nimport baseHttp from './baseHttp';\nclass DashboardService {\n  constructor() {\n    this.DASHBOARD_ENDPOINTS = {\n      STATS: '/api/dashboard/stats',\n      ANALYTICS: '/api/dashboard/analytics',\n      METRICS: '/api/dashboard/metrics',\n      USERS: '/api/dashboard/users',\n      BOOKINGS_OVERVIEW: '/api/dashboard/bookings/overview'\n    };\n  }\n  /**\n   * Get dashboard statistics\n   */\n  async getDashboardStats() {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.STATS);\n    } catch (error) {\n      console.error('Failed to get dashboard stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get analytics summary\n   */\n  async getAnalyticsSummary(days = 7) {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.ANALYTICS, {\n        params: {\n          days\n        }\n      });\n    } catch (error) {\n      console.error('Failed to get analytics summary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get system metrics\n   */\n  async getSystemMetrics() {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.METRICS);\n    } catch (error) {\n      console.error('Failed to get system metrics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get users overview\n   */\n  async getUsersOverview(limit = 50) {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.USERS, {\n        params: {\n          limit\n        }\n      });\n    } catch (error) {\n      console.error('Failed to get users overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get bookings overview\n   */\n  async getBookingsOverview(days = 7) {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.BOOKINGS_OVERVIEW, {\n        params: {\n          days\n        }\n      });\n    } catch (error) {\n      console.error('Failed to get bookings overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Format analytics data for charts\n   */\n  formatAnalyticsForChart(analytics) {\n    var _analytics$message_an, _analytics$message_an2, _analytics$message_an3, _analytics$booking_an, _analytics$booking_an2, _analytics$user_analy, _analytics$user_analy2;\n    const sentimentData = [{\n      name: 'Positive',\n      value: ((_analytics$message_an = analytics.message_analytics) === null || _analytics$message_an === void 0 ? void 0 : _analytics$message_an.positive_messages) || 0\n    }, {\n      name: 'Negative',\n      value: ((_analytics$message_an2 = analytics.message_analytics) === null || _analytics$message_an2 === void 0 ? void 0 : _analytics$message_an2.negative_messages) || 0\n    }, {\n      name: 'Neutral',\n      value: ((_analytics$message_an3 = analytics.message_analytics) === null || _analytics$message_an3 === void 0 ? void 0 : _analytics$message_an3.neutral_messages) || 0\n    }];\n    const bookingData = [{\n      name: 'Confirmed',\n      value: ((_analytics$booking_an = analytics.booking_analytics) === null || _analytics$booking_an === void 0 ? void 0 : _analytics$booking_an.confirmed_bookings) || 0\n    }, {\n      name: 'Cancelled',\n      value: ((_analytics$booking_an2 = analytics.booking_analytics) === null || _analytics$booking_an2 === void 0 ? void 0 : _analytics$booking_an2.cancelled_bookings) || 0\n    }];\n    const userActivityData = [{\n      name: 'New Users',\n      value: ((_analytics$user_analy = analytics.user_analytics) === null || _analytics$user_analy === void 0 ? void 0 : _analytics$user_analy.new_users) || 0\n    }, {\n      name: 'Active Users',\n      value: ((_analytics$user_analy2 = analytics.user_analytics) === null || _analytics$user_analy2 === void 0 ? void 0 : _analytics$user_analy2.active_users) || 0\n    }];\n    return {\n      sentimentData,\n      bookingData,\n      userActivityData\n    };\n  }\n\n  /**\n   * Format daily stats for line chart\n   */\n  formatDailyStatsForChart(dailyStats) {\n    return dailyStats.map(stat => ({\n      date: stat._id,\n      total: stat.count,\n      confirmed: stat.confirmed,\n      cancelled: stat.cancelled\n    }));\n  }\n\n  /**\n   * Calculate growth percentage\n   */\n  calculateGrowth(current, previous) {\n    if (previous === 0) {\n      return {\n        percentage: current > 0 ? 100 : 0,\n        isPositive: current > 0,\n        formatted: current > 0 ? '+100%' : '0%'\n      };\n    }\n    const percentage = (current - previous) / previous * 100;\n    const isPositive = percentage >= 0;\n    const formatted = `${isPositive ? '+' : ''}${percentage.toFixed(1)}%`;\n    return {\n      percentage: Math.abs(percentage),\n      isPositive,\n      formatted\n    };\n  }\n\n  /**\n   * Get status indicators\n   */\n  getStatusIndicators(metrics) {\n    return [{\n      name: 'Database',\n      status: metrics.database_status === 'connected' ? 'healthy' : 'error',\n      value: metrics.database_status,\n      description: 'MongoDB connection status'\n    }, {\n      name: 'Collections',\n      status: Object.keys(metrics.collections_info || {}).length > 0 ? 'healthy' : 'warning',\n      value: `${Object.keys(metrics.collections_info || {}).length} collections`,\n      description: 'Database collections status'\n    }, {\n      name: 'Performance',\n      status: 'healthy',\n      // Could implement actual performance checks\n      value: 'Good',\n      description: 'System performance metrics'\n    }];\n  }\n\n  /**\n   * Export analytics data as CSV\n   */\n  exportAnalyticsCSV(analytics) {\n    var _analytics$message_an4, _analytics$message_an5, _analytics$message_an6, _analytics$message_an7, _analytics$booking_an3, _analytics$booking_an4, _analytics$booking_an5, _analytics$user_analy3, _analytics$user_analy4;\n    const csvData = [['Metric', 'Value'], ['Date Range', `${analytics.date_range.start} to ${analytics.date_range.end}`], ['Total Messages', ((_analytics$message_an4 = analytics.message_analytics) === null || _analytics$message_an4 === void 0 ? void 0 : _analytics$message_an4.total_messages) || 0], ['Positive Messages', ((_analytics$message_an5 = analytics.message_analytics) === null || _analytics$message_an5 === void 0 ? void 0 : _analytics$message_an5.positive_messages) || 0], ['Negative Messages', ((_analytics$message_an6 = analytics.message_analytics) === null || _analytics$message_an6 === void 0 ? void 0 : _analytics$message_an6.negative_messages) || 0], ['Neutral Messages', ((_analytics$message_an7 = analytics.message_analytics) === null || _analytics$message_an7 === void 0 ? void 0 : _analytics$message_an7.neutral_messages) || 0], ['Total Bookings', ((_analytics$booking_an3 = analytics.booking_analytics) === null || _analytics$booking_an3 === void 0 ? void 0 : _analytics$booking_an3.total_bookings) || 0], ['Confirmed Bookings', ((_analytics$booking_an4 = analytics.booking_analytics) === null || _analytics$booking_an4 === void 0 ? void 0 : _analytics$booking_an4.confirmed_bookings) || 0], ['Cancelled Bookings', ((_analytics$booking_an5 = analytics.booking_analytics) === null || _analytics$booking_an5 === void 0 ? void 0 : _analytics$booking_an5.cancelled_bookings) || 0], ['New Users', ((_analytics$user_analy3 = analytics.user_analytics) === null || _analytics$user_analy3 === void 0 ? void 0 : _analytics$user_analy3.new_users) || 0], ['Active Users', ((_analytics$user_analy4 = analytics.user_analytics) === null || _analytics$user_analy4 === void 0 ? void 0 : _analytics$user_analy4.active_users) || 0]];\n    const csvContent = csvData.map(row => row.join(',')).join('\\n');\n    const blob = new Blob([csvContent], {\n      type: 'text/csv'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `analytics_${analytics.date_range.start}_to_${analytics.date_range.end}.csv`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  }\n\n  /**\n   * Format large numbers for display\n   */\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  /**\n   * Get time period options for analytics\n   */\n  getTimePeriodOptions() {\n    return [{\n      value: 1,\n      label: 'Last 24 hours'\n    }, {\n      value: 7,\n      label: 'Last 7 days'\n    }, {\n      value: 14,\n      label: 'Last 14 days'\n    }, {\n      value: 30,\n      label: 'Last 30 days'\n    }];\n  }\n\n  /**\n   * Generate dashboard summary text\n   */\n  generateSummaryText(stats) {\n    return `\nDashboard Summary\n\nTotal Users: ${this.formatNumber(stats.total_users)}\nTotal Messages: ${this.formatNumber(stats.total_messages)}\nTotal Bookings: ${this.formatNumber(stats.total_bookings)}\nActive Sessions: ${this.formatNumber(stats.active_sessions)}\n\nToday's Activity:\n- New Bookings: ${stats.today_bookings}\n- New Messages: ${stats.today_messages}\n\nGenerated: ${new Date().toLocaleString()}\n    `.trim();\n  }\n}\n\n// Create and export singleton instance\nconst dashboardService = new DashboardService();\nexport default dashboardService;\n\n// Export the class for testing purposes\nexport { DashboardService };", "map": {"version": 3, "names": ["baseHttp", "DashboardService", "constructor", "DASHBOARD_ENDPOINTS", "STATS", "ANALYTICS", "METRICS", "USERS", "BOOKINGS_OVERVIEW", "getDashboardStats", "get", "error", "console", "getAnalyticsSummary", "days", "params", "getSystemMetrics", "getUsersOverview", "limit", "getBookingsOverview", "formatAnalyticsForChart", "analytics", "_analytics$message_an", "_analytics$message_an2", "_analytics$message_an3", "_analytics$booking_an", "_analytics$booking_an2", "_analytics$user_analy", "_analytics$user_analy2", "sentimentData", "name", "value", "message_analytics", "positive_messages", "negative_messages", "neutral_messages", "bookingData", "booking_analytics", "confirmed_bookings", "cancelled_bookings", "userActivityData", "user_analytics", "new_users", "active_users", "formatDailyStatsForChart", "dailyStats", "map", "stat", "date", "_id", "total", "count", "confirmed", "cancelled", "calculateGrowth", "current", "previous", "percentage", "isPositive", "formatted", "toFixed", "Math", "abs", "getStatusIndicators", "metrics", "status", "database_status", "description", "Object", "keys", "collections_info", "length", "exportAnalyticsCSV", "_analytics$message_an4", "_analytics$message_an5", "_analytics$message_an6", "_analytics$message_an7", "_analytics$booking_an3", "_analytics$booking_an4", "_analytics$booking_an5", "_analytics$user_analy3", "_analytics$user_analy4", "csvData", "date_range", "start", "end", "total_messages", "total_bookings", "csv<PERSON><PERSON>nt", "row", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "formatNumber", "num", "toString", "getTimePeriodOptions", "label", "generateSummaryText", "stats", "total_users", "active_sessions", "today_bookings", "today_messages", "Date", "toLocaleString", "trim", "dashboardService"], "sources": ["/home/<USER>/Documents/nextai/langraph_test/frontend/src/services/dashboardService.ts"], "sourcesContent": ["/**\n * Dashboard service\n * Handles dashboard analytics, system metrics, and admin functionality\n */\nimport baseHttp from './baseHttp';\nimport { \n  DashboardStats, \n  AnalyticsSummary, \n  SystemMetrics,\n  User,\n  Booking \n} from '../types';\n\nclass DashboardService {\n  private readonly DASHBOARD_ENDPOINTS = {\n    STATS: '/api/dashboard/stats',\n    ANALYTICS: '/api/dashboard/analytics',\n    METRICS: '/api/dashboard/metrics',\n    USERS: '/api/dashboard/users',\n    BOOKINGS_OVERVIEW: '/api/dashboard/bookings/overview',\n  };\n\n  /**\n   * Get dashboard statistics\n   */\n  async getDashboardStats(): Promise<DashboardStats> {\n    try {\n      return await baseHttp.get<DashboardStats>(this.DASHBOARD_ENDPOINTS.STATS);\n    } catch (error) {\n      console.error('Failed to get dashboard stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get analytics summary\n   */\n  async getAnalyticsSummary(days: number = 7): Promise<AnalyticsSummary> {\n    try {\n      return await baseHttp.get<AnalyticsSummary>(this.DASHBOARD_ENDPOINTS.ANALYTICS, {\n        params: { days }\n      });\n    } catch (error) {\n      console.error('Failed to get analytics summary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get system metrics\n   */\n  async getSystemMetrics(): Promise<SystemMetrics> {\n    try {\n      return await baseHttp.get<SystemMetrics>(this.DASHBOARD_ENDPOINTS.METRICS);\n    } catch (error) {\n      console.error('Failed to get system metrics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get users overview\n   */\n  async getUsersOverview(limit: number = 50): Promise<{ users: User[]; total: number; limit: number }> {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.USERS, {\n        params: { limit }\n      });\n    } catch (error) {\n      console.error('Failed to get users overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get bookings overview\n   */\n  async getBookingsOverview(days: number = 7): Promise<{\n    bookings: Booking[];\n    daily_stats: Array<{\n      _id: string;\n      count: number;\n      confirmed: number;\n      cancelled: number;\n    }>;\n    total: number;\n    date_range: {\n      start: string;\n      end: string;\n      days: number;\n    };\n  }> {\n    try {\n      return await baseHttp.get(this.DASHBOARD_ENDPOINTS.BOOKINGS_OVERVIEW, {\n        params: { days }\n      });\n    } catch (error) {\n      console.error('Failed to get bookings overview:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Format analytics data for charts\n   */\n  formatAnalyticsForChart(analytics: AnalyticsSummary): {\n    sentimentData: Array<{ name: string; value: number }>;\n    bookingData: Array<{ name: string; value: number }>;\n    userActivityData: Array<{ name: string; value: number }>;\n  } {\n    const sentimentData = [\n      { name: 'Positive', value: analytics.message_analytics?.positive_messages || 0 },\n      { name: 'Negative', value: analytics.message_analytics?.negative_messages || 0 },\n      { name: 'Neutral', value: analytics.message_analytics?.neutral_messages || 0 },\n    ];\n\n    const bookingData = [\n      { name: 'Confirmed', value: analytics.booking_analytics?.confirmed_bookings || 0 },\n      { name: 'Cancelled', value: analytics.booking_analytics?.cancelled_bookings || 0 },\n    ];\n\n    const userActivityData = [\n      { name: 'New Users', value: analytics.user_analytics?.new_users || 0 },\n      { name: 'Active Users', value: analytics.user_analytics?.active_users || 0 },\n    ];\n\n    return {\n      sentimentData,\n      bookingData,\n      userActivityData,\n    };\n  }\n\n  /**\n   * Format daily stats for line chart\n   */\n  formatDailyStatsForChart(dailyStats: Array<{\n    _id: string;\n    count: number;\n    confirmed: number;\n    cancelled: number;\n  }>): Array<{\n    date: string;\n    total: number;\n    confirmed: number;\n    cancelled: number;\n  }> {\n    return dailyStats.map(stat => ({\n      date: stat._id,\n      total: stat.count,\n      confirmed: stat.confirmed,\n      cancelled: stat.cancelled,\n    }));\n  }\n\n  /**\n   * Calculate growth percentage\n   */\n  calculateGrowth(current: number, previous: number): {\n    percentage: number;\n    isPositive: boolean;\n    formatted: string;\n  } {\n    if (previous === 0) {\n      return {\n        percentage: current > 0 ? 100 : 0,\n        isPositive: current > 0,\n        formatted: current > 0 ? '+100%' : '0%',\n      };\n    }\n\n    const percentage = ((current - previous) / previous) * 100;\n    const isPositive = percentage >= 0;\n    const formatted = `${isPositive ? '+' : ''}${percentage.toFixed(1)}%`;\n\n    return {\n      percentage: Math.abs(percentage),\n      isPositive,\n      formatted,\n    };\n  }\n\n  /**\n   * Get status indicators\n   */\n  getStatusIndicators(metrics: SystemMetrics): Array<{\n    name: string;\n    status: 'healthy' | 'warning' | 'error';\n    value: string;\n    description: string;\n  }> {\n    return [\n      {\n        name: 'Database',\n        status: metrics.database_status === 'connected' ? 'healthy' : 'error',\n        value: metrics.database_status,\n        description: 'MongoDB connection status',\n      },\n      {\n        name: 'Collections',\n        status: Object.keys(metrics.collections_info || {}).length > 0 ? 'healthy' : 'warning',\n        value: `${Object.keys(metrics.collections_info || {}).length} collections`,\n        description: 'Database collections status',\n      },\n      {\n        name: 'Performance',\n        status: 'healthy', // Could implement actual performance checks\n        value: 'Good',\n        description: 'System performance metrics',\n      },\n    ];\n  }\n\n  /**\n   * Export analytics data as CSV\n   */\n  exportAnalyticsCSV(analytics: AnalyticsSummary): void {\n    const csvData = [\n      ['Metric', 'Value'],\n      ['Date Range', `${analytics.date_range.start} to ${analytics.date_range.end}`],\n      ['Total Messages', analytics.message_analytics?.total_messages || 0],\n      ['Positive Messages', analytics.message_analytics?.positive_messages || 0],\n      ['Negative Messages', analytics.message_analytics?.negative_messages || 0],\n      ['Neutral Messages', analytics.message_analytics?.neutral_messages || 0],\n      ['Total Bookings', analytics.booking_analytics?.total_bookings || 0],\n      ['Confirmed Bookings', analytics.booking_analytics?.confirmed_bookings || 0],\n      ['Cancelled Bookings', analytics.booking_analytics?.cancelled_bookings || 0],\n      ['New Users', analytics.user_analytics?.new_users || 0],\n      ['Active Users', analytics.user_analytics?.active_users || 0],\n    ];\n\n    const csvContent = csvData.map(row => row.join(',')).join('\\n');\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `analytics_${analytics.date_range.start}_to_${analytics.date_range.end}.csv`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    URL.revokeObjectURL(url);\n  }\n\n  /**\n   * Format large numbers for display\n   */\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  /**\n   * Get time period options for analytics\n   */\n  getTimePeriodOptions(): Array<{ value: number; label: string }> {\n    return [\n      { value: 1, label: 'Last 24 hours' },\n      { value: 7, label: 'Last 7 days' },\n      { value: 14, label: 'Last 14 days' },\n      { value: 30, label: 'Last 30 days' },\n    ];\n  }\n\n  /**\n   * Generate dashboard summary text\n   */\n  generateSummaryText(stats: DashboardStats): string {\n    return `\nDashboard Summary\n\nTotal Users: ${this.formatNumber(stats.total_users)}\nTotal Messages: ${this.formatNumber(stats.total_messages)}\nTotal Bookings: ${this.formatNumber(stats.total_bookings)}\nActive Sessions: ${this.formatNumber(stats.active_sessions)}\n\nToday's Activity:\n- New Bookings: ${stats.today_bookings}\n- New Messages: ${stats.today_messages}\n\nGenerated: ${new Date().toLocaleString()}\n    `.trim();\n  }\n}\n\n// Create and export singleton instance\nconst dashboardService = new DashboardService();\nexport default dashboardService;\n\n// Export the class for testing purposes\nexport { DashboardService };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,YAAY;AASjC,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IAAA,KACJC,mBAAmB,GAAG;MACrCC,KAAK,EAAE,sBAAsB;MAC7BC,SAAS,EAAE,0BAA0B;MACrCC,OAAO,EAAE,wBAAwB;MACjCC,KAAK,EAAE,sBAAsB;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;EAAA;EAED;AACF;AACA;EACE,MAAMC,iBAAiBA,CAAA,EAA4B;IACjD,IAAI;MACF,OAAO,MAAMT,QAAQ,CAACU,GAAG,CAAiB,IAAI,CAACP,mBAAmB,CAACC,KAAK,CAAC;IAC3E,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,mBAAmBA,CAACC,IAAY,GAAG,CAAC,EAA6B;IACrE,IAAI;MACF,OAAO,MAAMd,QAAQ,CAACU,GAAG,CAAmB,IAAI,CAACP,mBAAmB,CAACE,SAAS,EAAE;QAC9EU,MAAM,EAAE;UAAED;QAAK;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMK,gBAAgBA,CAAA,EAA2B;IAC/C,IAAI;MACF,OAAO,MAAMhB,QAAQ,CAACU,GAAG,CAAgB,IAAI,CAACP,mBAAmB,CAACG,OAAO,CAAC;IAC5E,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMM,gBAAgBA,CAACC,KAAa,GAAG,EAAE,EAA4D;IACnG,IAAI;MACF,OAAO,MAAMlB,QAAQ,CAACU,GAAG,CAAC,IAAI,CAACP,mBAAmB,CAACI,KAAK,EAAE;QACxDQ,MAAM,EAAE;UAAEG;QAAM;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,mBAAmBA,CAACL,IAAY,GAAG,CAAC,EAcvC;IACD,IAAI;MACF,OAAO,MAAMd,QAAQ,CAACU,GAAG,CAAC,IAAI,CAACP,mBAAmB,CAACK,iBAAiB,EAAE;QACpEO,MAAM,EAAE;UAAED;QAAK;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACES,uBAAuBA,CAACC,SAA2B,EAIjD;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACA,MAAMC,aAAa,GAAG,CACpB;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,EAAAT,qBAAA,GAAAD,SAAS,CAACW,iBAAiB,cAAAV,qBAAA,uBAA3BA,qBAAA,CAA6BW,iBAAiB,KAAI;IAAE,CAAC,EAChF;MAAEH,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,EAAAR,sBAAA,GAAAF,SAAS,CAACW,iBAAiB,cAAAT,sBAAA,uBAA3BA,sBAAA,CAA6BW,iBAAiB,KAAI;IAAE,CAAC,EAChF;MAAEJ,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,EAAAP,sBAAA,GAAAH,SAAS,CAACW,iBAAiB,cAAAR,sBAAA,uBAA3BA,sBAAA,CAA6BW,gBAAgB,KAAI;IAAE,CAAC,CAC/E;IAED,MAAMC,WAAW,GAAG,CAClB;MAAEN,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,EAAAN,qBAAA,GAAAJ,SAAS,CAACgB,iBAAiB,cAAAZ,qBAAA,uBAA3BA,qBAAA,CAA6Ba,kBAAkB,KAAI;IAAE,CAAC,EAClF;MAAER,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,EAAAL,sBAAA,GAAAL,SAAS,CAACgB,iBAAiB,cAAAX,sBAAA,uBAA3BA,sBAAA,CAA6Ba,kBAAkB,KAAI;IAAE,CAAC,CACnF;IAED,MAAMC,gBAAgB,GAAG,CACvB;MAAEV,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,EAAAJ,qBAAA,GAAAN,SAAS,CAACoB,cAAc,cAAAd,qBAAA,uBAAxBA,qBAAA,CAA0Be,SAAS,KAAI;IAAE,CAAC,EACtE;MAAEZ,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,EAAAH,sBAAA,GAAAP,SAAS,CAACoB,cAAc,cAAAb,sBAAA,uBAAxBA,sBAAA,CAA0Be,YAAY,KAAI;IAAE,CAAC,CAC7E;IAED,OAAO;MACLd,aAAa;MACbO,WAAW;MACXI;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEI,wBAAwBA,CAACC,UAKvB,EAKC;IACD,OAAOA,UAAU,CAACC,GAAG,CAACC,IAAI,KAAK;MAC7BC,IAAI,EAAED,IAAI,CAACE,GAAG;MACdC,KAAK,EAAEH,IAAI,CAACI,KAAK;MACjBC,SAAS,EAAEL,IAAI,CAACK,SAAS;MACzBC,SAAS,EAAEN,IAAI,CAACM;IAClB,CAAC,CAAC,CAAC;EACL;;EAEA;AACF;AACA;EACEC,eAAeA,CAACC,OAAe,EAAEC,QAAgB,EAI/C;IACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO;QACLC,UAAU,EAAEF,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;QACjCG,UAAU,EAAEH,OAAO,GAAG,CAAC;QACvBI,SAAS,EAAEJ,OAAO,GAAG,CAAC,GAAG,OAAO,GAAG;MACrC,CAAC;IACH;IAEA,MAAME,UAAU,GAAI,CAACF,OAAO,GAAGC,QAAQ,IAAIA,QAAQ,GAAI,GAAG;IAC1D,MAAME,UAAU,GAAGD,UAAU,IAAI,CAAC;IAClC,MAAME,SAAS,GAAG,GAAGD,UAAU,GAAG,GAAG,GAAG,EAAE,GAAGD,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG;IAErE,OAAO;MACLH,UAAU,EAAEI,IAAI,CAACC,GAAG,CAACL,UAAU,CAAC;MAChCC,UAAU;MACVC;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEI,mBAAmBA,CAACC,OAAsB,EAKvC;IACD,OAAO,CACL;MACElC,IAAI,EAAE,UAAU;MAChBmC,MAAM,EAAED,OAAO,CAACE,eAAe,KAAK,WAAW,GAAG,SAAS,GAAG,OAAO;MACrEnC,KAAK,EAAEiC,OAAO,CAACE,eAAe;MAC9BC,WAAW,EAAE;IACf,CAAC,EACD;MACErC,IAAI,EAAE,aAAa;MACnBmC,MAAM,EAAEG,MAAM,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MACtFxC,KAAK,EAAE,GAAGqC,MAAM,CAACC,IAAI,CAACL,OAAO,CAACM,gBAAgB,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,cAAc;MAC1EJ,WAAW,EAAE;IACf,CAAC,EACD;MACErC,IAAI,EAAE,aAAa;MACnBmC,MAAM,EAAE,SAAS;MAAE;MACnBlC,KAAK,EAAE,MAAM;MACboC,WAAW,EAAE;IACf,CAAC,CACF;EACH;;EAEA;AACF;AACA;EACEK,kBAAkBA,CAACnD,SAA2B,EAAQ;IAAA,IAAAoD,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACpD,MAAMC,OAAO,GAAG,CACd,CAAC,QAAQ,EAAE,OAAO,CAAC,EACnB,CAAC,YAAY,EAAE,GAAG7D,SAAS,CAAC8D,UAAU,CAACC,KAAK,OAAO/D,SAAS,CAAC8D,UAAU,CAACE,GAAG,EAAE,CAAC,EAC9E,CAAC,gBAAgB,EAAE,EAAAZ,sBAAA,GAAApD,SAAS,CAACW,iBAAiB,cAAAyC,sBAAA,uBAA3BA,sBAAA,CAA6Ba,cAAc,KAAI,CAAC,CAAC,EACpE,CAAC,mBAAmB,EAAE,EAAAZ,sBAAA,GAAArD,SAAS,CAACW,iBAAiB,cAAA0C,sBAAA,uBAA3BA,sBAAA,CAA6BzC,iBAAiB,KAAI,CAAC,CAAC,EAC1E,CAAC,mBAAmB,EAAE,EAAA0C,sBAAA,GAAAtD,SAAS,CAACW,iBAAiB,cAAA2C,sBAAA,uBAA3BA,sBAAA,CAA6BzC,iBAAiB,KAAI,CAAC,CAAC,EAC1E,CAAC,kBAAkB,EAAE,EAAA0C,sBAAA,GAAAvD,SAAS,CAACW,iBAAiB,cAAA4C,sBAAA,uBAA3BA,sBAAA,CAA6BzC,gBAAgB,KAAI,CAAC,CAAC,EACxE,CAAC,gBAAgB,EAAE,EAAA0C,sBAAA,GAAAxD,SAAS,CAACgB,iBAAiB,cAAAwC,sBAAA,uBAA3BA,sBAAA,CAA6BU,cAAc,KAAI,CAAC,CAAC,EACpE,CAAC,oBAAoB,EAAE,EAAAT,sBAAA,GAAAzD,SAAS,CAACgB,iBAAiB,cAAAyC,sBAAA,uBAA3BA,sBAAA,CAA6BxC,kBAAkB,KAAI,CAAC,CAAC,EAC5E,CAAC,oBAAoB,EAAE,EAAAyC,sBAAA,GAAA1D,SAAS,CAACgB,iBAAiB,cAAA0C,sBAAA,uBAA3BA,sBAAA,CAA6BxC,kBAAkB,KAAI,CAAC,CAAC,EAC5E,CAAC,WAAW,EAAE,EAAAyC,sBAAA,GAAA3D,SAAS,CAACoB,cAAc,cAAAuC,sBAAA,uBAAxBA,sBAAA,CAA0BtC,SAAS,KAAI,CAAC,CAAC,EACvD,CAAC,cAAc,EAAE,EAAAuC,sBAAA,GAAA5D,SAAS,CAACoB,cAAc,cAAAwC,sBAAA,uBAAxBA,sBAAA,CAA0BtC,YAAY,KAAI,CAAC,CAAC,CAC9D;IAED,MAAM6C,UAAU,GAAGN,OAAO,CAACpC,GAAG,CAAC2C,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IAC/D,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,UAAU,CAAC,EAAE;MAAEK,IAAI,EAAE;IAAW,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAErC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,aAAahF,SAAS,CAAC8D,UAAU,CAACC,KAAK,OAAO/D,SAAS,CAAC8D,UAAU,CAACE,GAAG,MAAM;IAC5Fa,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAE/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B;;EAEA;AACF;AACA;EACEa,YAAYA,CAACC,GAAW,EAAU;IAChC,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEhD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACzC,CAAC,MAAM,IAAIgD,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACtC;IACA,OAAOgD,GAAG,CAACC,QAAQ,CAAC,CAAC;EACvB;;EAEA;AACF;AACA;EACEC,oBAAoBA,CAAA,EAA4C;IAC9D,OAAO,CACL;MAAE/E,KAAK,EAAE,CAAC;MAAEgF,KAAK,EAAE;IAAgB,CAAC,EACpC;MAAEhF,KAAK,EAAE,CAAC;MAAEgF,KAAK,EAAE;IAAc,CAAC,EAClC;MAAEhF,KAAK,EAAE,EAAE;MAAEgF,KAAK,EAAE;IAAe,CAAC,EACpC;MAAEhF,KAAK,EAAE,EAAE;MAAEgF,KAAK,EAAE;IAAe,CAAC,CACrC;EACH;;EAEA;AACF;AACA;EACEC,mBAAmBA,CAACC,KAAqB,EAAU;IACjD,OAAO;AACX;AACA;AACA,eAAe,IAAI,CAACN,YAAY,CAACM,KAAK,CAACC,WAAW,CAAC;AACnD,kBAAkB,IAAI,CAACP,YAAY,CAACM,KAAK,CAAC3B,cAAc,CAAC;AACzD,kBAAkB,IAAI,CAACqB,YAAY,CAACM,KAAK,CAAC1B,cAAc,CAAC;AACzD,mBAAmB,IAAI,CAACoB,YAAY,CAACM,KAAK,CAACE,eAAe,CAAC;AAC3D;AACA;AACA,kBAAkBF,KAAK,CAACG,cAAc;AACtC,kBAAkBH,KAAK,CAACI,cAAc;AACtC;AACA,aAAa,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;AACxC,KAAK,CAACC,IAAI,CAAC,CAAC;EACV;AACF;;AAEA;AACA,MAAMC,gBAAgB,GAAG,IAAIxH,gBAAgB,CAAC,CAAC;AAC/C,eAAewH,gBAAgB;;AAE/B;AACA,SAASxH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}